const { getDatabase } = require('../src/lib/database.ts');

async function cleanTestData() {
  console.log('🧹 Cleaning test data...\n');

  try {
    const db = await getDatabase();
    
    // Delete all verification codes
    db.run('DELETE FROM verification_codes', (err) => {
      if (err) {
        console.error('Error:', err);
        return;
      }
      console.log('✅ All verification codes deleted');
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Clean failed:', error);
    process.exit(1);
  }
}

// Run the clean
cleanTestData();
