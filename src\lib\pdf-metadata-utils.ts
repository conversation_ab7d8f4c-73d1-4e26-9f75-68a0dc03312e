/**
 * Utility functions for handling PDF metadata in the LDIS system
 */

export interface DocumentMetadata {
  document_name: string;
  applicant_name: string;
  document_data: {
    [key: string]: string;
  };
  generation_info: {
    system: string;
  };
}

/**
 * Extract JSON metadata from PDF text content
 * This function would be used if you have access to the PDF's text content
 * @param pdfTextContent - The text content extracted from a PDF
 * @returns The parsed metadata object or null if not found
 */
export function extractMetadataFromPdfText(pdfTextContent: string): DocumentMetadata | null {
  try {
    // Look for the LDIS_METADATA marker in the text
    const metadataMatch = pdfTextContent.match(/LDIS_METADATA:({.*})/);
    
    if (metadataMatch && metadataMatch[1]) {
      const metadataJson = metadataMatch[1];
      return JSON.parse(metadataJson) as DocumentMetadata;
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting metadata from PDF text:', error);
    return null;
  }
}

/**
 * Validate document metadata structure
 * @param metadata - The metadata object to validate
 * @returns True if the metadata has the expected structure
 */
export function validateDocumentMetadata(metadata: unknown): metadata is DocumentMetadata {
  if (!metadata || typeof metadata !== 'object') {
    return false;
  }

  const obj = metadata as Record<string, unknown>;

  return (
    typeof obj.document_name === 'string' &&
    typeof obj.applicant_name === 'string' &&
    typeof obj.document_data === 'object' &&
    typeof obj.generation_info === 'object' &&
    obj.generation_info !== null &&
    typeof (obj.generation_info as Record<string, unknown>).system === 'string'
  );
}

/**
 * Format applicant name from metadata
 * @param metadata - The document metadata
 * @returns Formatted applicant name
 */
export function formatApplicantName(metadata: DocumentMetadata): string {
  return metadata.applicant_name || 'Unknown Applicant';
}

/**
 * Get document generation system from metadata
 * @param metadata - The document metadata
 * @returns System name
 */
export function getDocumentGenerationSystem(metadata: DocumentMetadata): string {
  return metadata.generation_info.system || 'Unknown System';
}

/**
 * Extract specific field value from document data
 * @param metadata - The document metadata
 * @param fieldName - The field name to extract
 * @returns The field value or empty string if not found
 */
export function getDocumentField(metadata: DocumentMetadata, fieldName: string): string {
  return metadata.document_data[fieldName] || '';
}

/**
 * Get all available fields from document data
 * @param metadata - The document metadata
 * @returns Array of field names
 */
export function getAvailableFields(metadata: DocumentMetadata): string[] {
  return Object.keys(metadata.document_data);
}

/**
 * Create a summary of the document from metadata
 * @param metadata - The document metadata
 * @returns Document summary object
 */
export function createDocumentSummary(metadata: DocumentMetadata) {
  return {
    documentName: metadata.document_name,
    applicantName: metadata.applicant_name,
    system: metadata.generation_info.system,
    fieldCount: Object.keys(metadata.document_data).length,
    hasPhoto: 'applicants_photo' in metadata.document_data
  };
}

/**
 * Example usage and demonstration
 */
export const EXAMPLE_METADATA: DocumentMetadata = {
  document_name: "MEDICAL CERTIFICATE.pdf",
  applicant_name: "Ortega, Brent Philip, L., D.",
  document_data: {
    LAST_NAME: "Ortega",
    FIRST_NAME: "Brent Philip",
    MIDDLE_INITIAL: "L.",
    SUFFIX: "D.",
    AGE: "21",
    BARANGAY: "Malbon",
    MUNICIPALITY: "Tanauan",
    PROVINCE: "Leyte",
    PARAGRAPH: "Oaoao",
    DAY: "31",
    MONTH: "July",
    YEAR: "2025",
    OR_NUMBER: "34224434"
  },
  generation_info: {
    system: "Legal Document Issuance System"
  }
};
