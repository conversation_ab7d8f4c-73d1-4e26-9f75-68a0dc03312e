/**
 * Utility functions for search and sort operations
 */

export interface SearchableItem {
  [key: string]: any;
}

/**
 * Search items by multiple fields
 */
export function searchItems<T extends SearchableItem>(
  items: T[],
  searchTerm: string,
  searchFields: string[]
): T[] {
  if (!searchTerm.trim()) {
    return items;
  }

  const normalizedSearchTerm = searchTerm.toLowerCase().trim();
  
  return items.filter((item) => {
    return searchFields.some((field) => {
      const fieldValue = getNestedValue(item, field);
      if (fieldValue == null) return false;
      
      return String(fieldValue)
        .toLowerCase()
        .includes(normalizedSearchTerm);
    });
  });
}

/**
 * Sort items by a specific field
 */
export function sortItems<T extends SearchableItem>(
  items: T[],
  sortField: string,
  sortOrder: "asc" | "desc" = "asc"
): T[] {
  return [...items].sort((a, b) => {
    const aValue = getNestedValue(a, sortField);
    const bValue = getNestedValue(b, sortField);
    
    // Handle null/undefined values
    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return sortOrder === "asc" ? 1 : -1;
    if (bValue == null) return sortOrder === "asc" ? -1 : 1;
    
    // Handle different data types
    let comparison = 0;
    
    if (typeof aValue === "string" && typeof bValue === "string") {
      comparison = aValue.localeCompare(bValue);
    } else if (typeof aValue === "number" && typeof bValue === "number") {
      comparison = aValue - bValue;
    } else if (aValue instanceof Date && bValue instanceof Date) {
      comparison = aValue.getTime() - bValue.getTime();
    } else {
      // Convert to strings for comparison
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();
      comparison = aStr.localeCompare(bStr);
    }
    
    return sortOrder === "asc" ? comparison : -comparison;
  });
}

/**
 * Filter items by multiple criteria
 */
export function filterItems<T extends SearchableItem>(
  items: T[],
  filters: { field: string; values: string[] }[]
): T[] {
  if (filters.length === 0) {
    return items;
  }

  return items.filter((item) => {
    return filters.every(({ field, values }) => {
      if (values.length === 0) return true;
      
      const fieldValue = getNestedValue(item, field);
      if (fieldValue == null) return false;
      
      return values.includes(String(fieldValue));
    });
  });
}

/**
 * Combined search, filter, and sort operation
 */
export function searchFilterSort<T extends SearchableItem>(
  items: T[],
  options: {
    searchTerm?: string;
    searchFields?: string[];
    filters?: { field: string; values: string[] }[];
    sortField?: string;
    sortOrder?: "asc" | "desc";
  }
): T[] {
  let result = items;
  
  // Apply search
  if (options.searchTerm && options.searchFields) {
    result = searchItems(result, options.searchTerm, options.searchFields);
  }
  
  // Apply filters
  if (options.filters) {
    result = filterItems(result, options.filters);
  }
  
  // Apply sort
  if (options.sortField) {
    result = sortItems(result, options.sortField, options.sortOrder);
  }
  
  return result;
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current?.[key];
  }, obj);
}

/**
 * Format date for sorting (converts string dates to Date objects)
 */
export function prepareDateForSort(dateString: string | null | undefined): Date | null {
  if (!dateString) return null;
  
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Get unique values for a field (useful for generating filter options)
 */
export function getUniqueFieldValues<T extends SearchableItem>(
  items: T[],
  field: string
): string[] {
  const values = items
    .map(item => getNestedValue(item, field))
    .filter(value => value != null)
    .map(value => String(value));
  
  return Array.from(new Set(values)).sort();
}

/**
 * Create filter options from unique field values
 */
export function createFilterOptions(
  items: SearchableItem[],
  field: string,
  labelFormatter?: (value: string) => string
): { value: string; label: string; field: string }[] {
  const uniqueValues = getUniqueFieldValues(items, field);
  
  return uniqueValues.map(value => ({
    value,
    label: labelFormatter ? labelFormatter(value) : value,
    field
  }));
}

/**
 * Debounce function for search input
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Highlight search terms in text
 */
export function highlightSearchTerm(
  text: string,
  searchTerm: string,
  className: string = "bg-yellow-200 dark:bg-yellow-800"
): string {
  if (!searchTerm.trim()) return text;
  
  const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, `<span class="${className}">$1</span>`);
}
