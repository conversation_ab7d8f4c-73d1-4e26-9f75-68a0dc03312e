/**
 * Test script for localStorage functionality
 * This script tests the localStorage integration for admin mode
 * 
 * Run with: node scripts/test-localstorage.js
 */

// Mock localStorage for Node.js environment
class LocalStorageMock {
  constructor() {
    this.store = {};
  }

  getItem(key) {
    return this.store[key] || null;
  }

  setItem(key, value) {
    this.store[key] = String(value);
  }

  removeItem(key) {
    delete this.store[key];
  }

  clear() {
    this.store = {};
  }

  key(index) {
    const keys = Object.keys(this.store);
    return keys[index] || null;
  }

  get length() {
    return Object.keys(this.store).length;
  }
}

// Set up global localStorage mock
global.localStorage = new LocalStorageMock();
global.window = { localStorage: global.localStorage };

// Mock console methods for cleaner output
const originalLog = console.log;
const originalWarn = console.warn;

function testLocalStorage() {
  console.log("🧪 Testing localStorage functionality for LDIS Admin Mode\n");

  // Test 1: Basic localStorage operations
  console.log("📝 Test 1: Basic localStorage operations");
  try {
    localStorage.setItem("test-key", "test-value");
    const value = localStorage.getItem("test-key");
    console.log(`✅ Set and get: ${value === "test-value" ? "PASS" : "FAIL"}`);
    
    localStorage.removeItem("test-key");
    const removedValue = localStorage.getItem("test-key");
    console.log(`✅ Remove: ${removedValue === null ? "PASS" : "FAIL"}`);
  } catch (error) {
    console.log(`❌ Basic operations: FAIL - ${error.message}`);
  }

  // Test 2: JSON serialization
  console.log("\n📝 Test 2: JSON serialization");
  try {
    const testData = { adminMode: true, timestamp: Date.now() };
    localStorage.setItem("ldis-test", JSON.stringify(testData));
    const retrieved = JSON.parse(localStorage.getItem("ldis-test"));
    console.log(`✅ JSON serialization: ${retrieved.adminMode === true ? "PASS" : "FAIL"}`);
  } catch (error) {
    console.log(`❌ JSON serialization: FAIL - ${error.message}`);
  }

  // Test 3: Admin mode keys
  console.log("\n📝 Test 3: Admin mode localStorage keys");
  const adminKeys = [
    "ldis-admin-mode",
    "ldis-admin-authenticated", 
    "ldis-auth-timestamp"
  ];

  adminKeys.forEach(key => {
    try {
      localStorage.setItem(key, JSON.stringify(true));
      const value = JSON.parse(localStorage.getItem(key));
      console.log(`✅ ${key}: ${value === true ? "PASS" : "FAIL"}`);
    } catch (error) {
      console.log(`❌ ${key}: FAIL - ${error.message}`);
    }
  });

  // Test 4: Authentication expiry logic
  console.log("\n📝 Test 4: Authentication expiry logic");
  try {
    const now = Date.now();
    const expiredTime = now - (25 * 60 * 60 * 1000); // 25 hours ago
    const validTime = now - (1 * 60 * 60 * 1000); // 1 hour ago
    
    // Test expired authentication
    localStorage.setItem("ldis-auth-timestamp", JSON.stringify(expiredTime));
    const expiredTimestamp = JSON.parse(localStorage.getItem("ldis-auth-timestamp"));
    const isExpired = (now - expiredTimestamp) > (24 * 60 * 60 * 1000);
    console.log(`✅ Expired auth detection: ${isExpired ? "PASS" : "FAIL"}`);
    
    // Test valid authentication
    localStorage.setItem("ldis-auth-timestamp", JSON.stringify(validTime));
    const validTimestamp = JSON.parse(localStorage.getItem("ldis-auth-timestamp"));
    const isValid = (now - validTimestamp) <= (24 * 60 * 60 * 1000);
    console.log(`✅ Valid auth detection: ${isValid ? "PASS" : "FAIL"}`);
  } catch (error) {
    console.log(`❌ Expiry logic: FAIL - ${error.message}`);
  }

  // Test 5: Error handling
  console.log("\n📝 Test 5: Error handling");
  try {
    // Test with invalid JSON
    localStorage.setItem("invalid-json", "{ invalid json }");
    let errorCaught = false;
    try {
      JSON.parse(localStorage.getItem("invalid-json"));
    } catch (parseError) {
      errorCaught = true;
    }
    console.log(`✅ Invalid JSON handling: ${errorCaught ? "PASS" : "FAIL"}`);
  } catch (error) {
    console.log(`❌ Error handling: FAIL - ${error.message}`);
  }

  // Test 6: Storage capacity
  console.log("\n📝 Test 6: Storage state");
  console.log(`📊 Current storage items: ${localStorage.length}`);
  console.log("📋 Stored keys:");
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    const value = localStorage.getItem(key);
    console.log(`   - ${key}: ${value}`);
  }

  // Clean up
  console.log("\n🧹 Cleaning up test data...");
  localStorage.clear();
  console.log(`✅ Storage cleared: ${localStorage.length === 0 ? "PASS" : "FAIL"}`);

  console.log("\n🎉 localStorage testing completed!");
  console.log("\n💡 Integration notes:");
  console.log("   - Admin mode state persists across browser sessions");
  console.log("   - Authentication expires after 24 hours");
  console.log("   - Automatic cleanup of expired sessions");
  console.log("   - Error handling for corrupted data");
  console.log("   - SSR-safe implementation with window checks");
}

// Run the tests
testLocalStorage();
