"use client";

import { useState, useEffect, useRef } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, Save, Upload, X } from "lucide-react";
import { toast } from "sonner";

interface Document {
  id: number;
  document_name: string;
  applicant_name: string;
  uploaded_at: string;
  document_data: Buffer | null;
  status: string;
  approved_at: string | null;
  user_id: number;
}

interface DocumentData {
  [key: string]: string;
}

interface EditDocumentDialogProps {
  document: Document | null;
  documentData: DocumentData;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDocumentUpdated: (updatedDocument: Document) => void;
}

const STATUS_OPTIONS = [
  { value: "to review", label: "To Review" },
  { value: "approved", label: "Approved" },
  { value: "rejected", label: "Rejected" },
  { value: "uploaded", label: "Uploaded" },
  { value: "restored", label: "Restored" },
];

export function EditDocumentDialog({
  document,
  documentData,
  open,
  onOpenChange,
  onDocumentUpdated,
}: EditDocumentDialogProps) {
  const [status, setStatus] = useState("to review");
  const [editableData, setEditableData] = useState<DocumentData>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [photoPreview, setPhotoPreview] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Reset form when document changes or dialog opens
  useEffect(() => {
    if (document && open) {
      setStatus(document.status);

      // Update date fields automatically
      const updatedData = { ...documentData };
      const now = new Date();

      // Auto-update date fields
      if (updatedData.hasOwnProperty("DAY")) {
        updatedData.DAY = now.getDate().toString();
      }
      if (updatedData.hasOwnProperty("MONTH")) {
        updatedData.MONTH = now.toLocaleDateString("en-US", { month: "long" });
      }
      if (updatedData.hasOwnProperty("YEAR")) {
        updatedData.YEAR = now.getFullYear().toString();
      }

      setEditableData(updatedData);
      setPhotoPreview(documentData.applicants_photo || "");
      setError("");
    }
  }, [document, documentData, open]);

  // Handle photo upload
  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please select a valid image file");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image size must be less than 5MB");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const base64String = e.target?.result as string;
      setPhotoPreview(base64String);

      // Update the editable data
      setEditableData((prev) => ({
        ...prev,
        applicants_photo: base64String,
      }));
    };
    reader.readAsDataURL(file);
  };

  // Handle photo removal
  const handlePhotoRemove = () => {
    setPhotoPreview("");
    setEditableData((prev) => ({
      ...prev,
      applicants_photo: "",
    }));

    // Clear the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!document) return;

    setLoading(true);
    setError("");

    try {
      // Update document data
      const updatedDocumentData = JSON.stringify(editableData);

      const response = await fetch(`/api/documents/${document.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: status,
          document_data: updatedDocumentData,
          approved_at: status === "approved" ? new Date().toISOString() : null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update document");
      }

      const data = await response.json();

      // Update the document in the parent component
      onDocumentUpdated(data.document);

      // Close the dialog
      onOpenChange(false);

      toast.success("Document updated successfully");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to update document";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (!loading) {
      onOpenChange(false);
      setError("");
    }
  };

  // Handle data field changes
  const handleDataFieldChange = (key: string, value: string) => {
    setEditableData((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  if (!document) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            Edit Document
          </DialogTitle>
          <DialogDescription>
            Update the document information and data for &quot;
            {document.document_name}
            &quot;.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Document Name (Read-only) */}
          <div className="space-y-2">
            <Label htmlFor="documentName">Document Name</Label>
            <Input
              id="documentName"
              value={document.document_name}
              disabled
              className="bg-muted"
            />
            <p className="text-xs text-muted-foreground">
              Document name cannot be changed
            </p>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={status} onValueChange={setStatus} disabled={loading}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {STATUS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Applicant Photo */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Applicant Photo</Label>
            <div className="space-y-3 border rounded-md p-3">
              {photoPreview ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Current Photo</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handlePhotoRemove}
                      disabled={loading}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Remove
                    </Button>
                  </div>
                  <div className="flex justify-center">
                    <img
                      src={photoPreview}
                      alt="Applicant photo preview"
                      className="max-w-[200px] max-h-[200px] object-cover rounded-lg border"
                    />
                  </div>
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No photo uploaded
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="photoUpload" className="text-sm">
                  {photoPreview ? "Replace Photo" : "Upload Photo"}
                </Label>
                <div className="flex items-center gap-2">
                  <Input
                    ref={fileInputRef}
                    id="photoUpload"
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoUpload}
                    disabled={loading}
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={loading}
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    {photoPreview ? "Replace Photo" : "Upload Photo"}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Supported formats: JPG, PNG, GIF. Max size: 5MB
                </p>
              </div>
            </div>
          </div>

          {/* Document Data Fields */}
          {Object.keys(editableData).length > 0 && (
            <div className="space-y-4">
              <Label className="text-base font-semibold">Document Data</Label>
              <div className="space-y-3 border rounded-md p-3">
                {Object.entries(editableData).map(([key, value]) => {
                  // Skip applicants_photo as it has its own dedicated section
                  if (key === "applicants_photo") {
                    return null;
                  }

                  // Check if this is a date field that should be read-only
                  const isDateField = ["DAY", "MONTH", "YEAR"].includes(key);

                  return (
                    <div key={key} className="space-y-2">
                      <Label htmlFor={key} className="text-sm">
                        {key
                          .replace(/_/g, " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                        {isDateField && (
                          <span className="text-xs text-muted-foreground ml-2">
                            (Auto-updated)
                          </span>
                        )}
                      </Label>
                      <Input
                        id={key}
                        value={value || ""}
                        onChange={(e) =>
                          handleDataFieldChange(key, e.target.value)
                        }
                        disabled={loading || isDateField}
                        readOnly={isDateField}
                        className={`text-sm ${
                          isDateField ? "bg-muted cursor-not-allowed" : ""
                        }`}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
