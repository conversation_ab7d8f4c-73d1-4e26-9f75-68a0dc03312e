import { NextResponse } from 'next/server';
import { getAllDocuments } from '@/lib/database';

/**
 * GET /api/documents - Get all documents
 */
export async function GET() {
  try {
    const documents = await getAllDocuments();

    // Ensure uploaded_at is properly formatted for all documents
    const formattedDocuments = documents.map(doc => ({
      ...doc,
      uploaded_at: doc.uploaded_at || new Date().toISOString()
    }));

    return NextResponse.json({
      documents: formattedDocuments,
      count: formattedDocuments.length
    });
  } catch (error) {
    console.error('Error fetching documents:', error);
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    );
  }
}
