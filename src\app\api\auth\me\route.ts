import { NextResponse } from 'next/server';
import { getAllUsers } from '@/lib/database';

/**
 * GET /api/auth/me - Get current user information
 * Since this is a single-user system, return the first (and only) user
 */
export async function GET() {
  try {
    const users = await getAllUsers();
    
    if (users.length === 0) {
      return NextResponse.json(
        { error: 'No users found in the system' },
        { status: 404 }
      );
    }
    
    // Return the first user (admin) without sensitive information
    const user = users[0];
    return NextResponse.json({
      id: user.id,
      username: user.username,
      role: user.role,
      created_at: user.created_at
    });
    
  } catch (error) {
    console.error('Error fetching user information:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user information' },
      { status: 500 }
    );
  }
}
