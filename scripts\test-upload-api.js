const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function testUploadAPI() {
  console.log('🧪 Testing Template Upload API\n');

  try {
    // Read the test HTML file
    const htmlFilePath = path.join(__dirname, '..', 'test-template.html');
    if (!fs.existsSync(htmlFilePath)) {
      console.error('❌ Test HTML file not found:', htmlFilePath);
      return;
    }

    const htmlContent = fs.readFileSync(htmlFilePath);
    console.log('📄 Test HTML file loaded:', htmlFilePath);
    console.log('📏 File size:', htmlContent.length, 'bytes\n');

    // Get user information first
    console.log('👤 Getting user information...');
    const userResponse = await fetch('http://localhost:3000/api/auth/me');
    
    if (!userResponse.ok) {
      console.error('❌ Failed to get user information:', userResponse.status);
      const errorText = await userResponse.text();
      console.error('Error response:', errorText);
      return;
    }

    const userData = await userResponse.json();
    console.log('✅ User data:', userData);
    console.log('');

    // Create form data
    const formData = new FormData();
    formData.append('templateName', 'Test Certificate Template');
    formData.append('description', 'A test certificate template for API testing');
    formData.append('layoutSize', 'A4');
    formData.append('userId', userData.id.toString());
    formData.append('htmlFile', htmlContent, {
      filename: 'test-template.html',
      contentType: 'text/html'
    });

    console.log('📤 Uploading template...');
    console.log('Template Name: Test Certificate Template');
    console.log('User ID:', userData.id);
    console.log('');

    // Upload the template
    const uploadResponse = await fetch('http://localhost:3000/api/templates/upload', {
      method: 'POST',
      body: formData
    });

    const result = await uploadResponse.json();

    if (uploadResponse.ok) {
      console.log('✅ Upload successful!');
      console.log('Template ID:', result.templateId);
      console.log('Template Name:', result.templateName);
      console.log('Placeholders found:', result.placeholders);
      console.log('Has images:', result.hasImages);
      console.log('Image count:', result.imageCount);
    } else {
      console.error('❌ Upload failed:', uploadResponse.status);
      console.error('Error:', result.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Check if the development server is running
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/me');
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('⚠️ Development server is not running on http://localhost:3000');
    console.log('Please start the server with: pnpm dev');
    console.log('Then run this test again.');
    return;
  }

  await testUploadAPI();
}

main();
