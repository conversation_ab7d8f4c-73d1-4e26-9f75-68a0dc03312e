import { NextRequest, NextResponse } from 'next/server';
import { getNotificationById, deleteNotification, markNotificationAsRead } from '@/lib/database';

/**
 * DELETE /api/notifications/[id] - Delete a notification
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const notificationId = parseInt(id);
    
    if (isNaN(notificationId)) {
      return NextResponse.json(
        { error: 'Invalid notification ID' },
        { status: 400 }
      );
    }

    // Get notification from database first to verify it exists
    const notification = await getNotificationById(notificationId);
    
    if (!notification) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Delete notification from database
    await deleteNotification(notificationId);

    return NextResponse.json(
      { 
        message: 'Notification deleted successfully',
        notificationId
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { error: 'Failed to delete notification' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/notifications/[id] - Mark notification as read
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const notificationId = parseInt(id);
    
    if (isNaN(notificationId)) {
      return NextResponse.json(
        { error: 'Invalid notification ID' },
        { status: 400 }
      );
    }

    // Get notification from database first to verify it exists
    const notification = await getNotificationById(notificationId);
    
    if (!notification) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Mark notification as read
    await markNotificationAsRead(notificationId);

    return NextResponse.json(
      { 
        message: 'Notification marked as read',
        notificationId
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error marking notification as read:', error);
    return NextResponse.json(
      { error: 'Failed to mark notification as read' },
      { status: 500 }
    );
  }
}
