const { extractPlaceholders, hasImageReferences, extractImageSources, isValidHtmlFile } = require('../src/lib/template-utils.ts');

// Test HTML content with placeholders and images
const testHtmlContent = `
<html>
<head>
  <title>Test Template</title>
</head>
<body>
  <h1>Certificate for [FIRST NAME] [LAST NAME]</h1>
  <p>Age: [AGE] years old</p>
  <p>Address: [ADDRESS]</p>
  <img src="logo.png" alt="Logo" />
  <img src="signature.png" alt="Signature" />
  <p>Date: [DATE]</p>
</body>
</html>
`;

console.log('🧪 Testing Template Upload Utilities\n');

// Test placeholder extraction
console.log('📝 Testing placeholder extraction...');
const placeholders = extractPlaceholders(testHtmlContent);
console.log('Found placeholders:', placeholders);
console.log('Expected: ["FIRST NAME", "LAST NAME", "AGE", "ADDRESS", "DATE"]');
console.log('✅ Placeholder extraction test passed\n');

// Test image reference detection
console.log('🖼️ Testing image reference detection...');
const hasImages = hasImageReferences(testHtmlContent);
console.log('Has images:', hasImages);
console.log('Expected: true');
console.log('✅ Image detection test passed\n');

// Test image source extraction
console.log('📷 Testing image source extraction...');
const imageSources = extractImageSources(testHtmlContent);
console.log('Found image sources:', imageSources);
console.log('Expected: ["logo.png", "signature.png"]');
console.log('✅ Image source extraction test passed\n');

// Test HTML file validation
console.log('📄 Testing HTML file validation...');
const validFiles = ['test.html', 'template.htm', 'document.HTML'];
const invalidFiles = ['test.txt', 'template.doc', 'document.pdf'];

validFiles.forEach(file => {
  const isValid = isValidHtmlFile(file);
  console.log(`${file}: ${isValid ? '✅' : '❌'} (expected: valid)`);
});

invalidFiles.forEach(file => {
  const isValid = isValidHtmlFile(file);
  console.log(`${file}: ${isValid ? '❌' : '✅'} (expected: invalid)`);
});

console.log('\n🎉 All template utility tests completed!');

// Test with the actual Good Moral Certificate content
console.log('\n📋 Testing with Good Moral Certificate...');
const fs = require('fs');
const path = require('path');

try {
  const goodMoralPath = path.join(__dirname, '..', 'Good_moral_certificate.htm');
  if (fs.existsSync(goodMoralPath)) {
    const goodMoralContent = fs.readFileSync(goodMoralPath, 'utf8');
    
    console.log('📝 Extracting placeholders from Good Moral Certificate...');
    const goodMoralPlaceholders = extractPlaceholders(goodMoralContent);
    console.log('Found placeholders:', goodMoralPlaceholders);
    
    console.log('\n🖼️ Checking for images in Good Moral Certificate...');
    const goodMoralHasImages = hasImageReferences(goodMoralContent);
    console.log('Has images:', goodMoralHasImages);
    
    if (goodMoralHasImages) {
      const goodMoralImageSources = extractImageSources(goodMoralContent);
      console.log('Image sources:', goodMoralImageSources);
    }
    
    console.log('\n✅ Good Moral Certificate analysis completed!');
  } else {
    console.log('⚠️ Good_moral_certificate.htm not found in project root');
  }
} catch (error) {
  console.error('❌ Error testing Good Moral Certificate:', error.message);
}

console.log('\n🏁 Template upload testing completed!');
