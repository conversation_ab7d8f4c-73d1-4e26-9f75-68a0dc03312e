const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🔧 Fixing Existing Notification Timestamps...');
console.log('📍 Database path:', dbPath);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Fix existing notification timestamps
async function fixExistingNotificationTimestamps() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n🔍 Checking existing notifications with old timestamps...');
      
      // Get all notifications and check their timestamps
      db.all(`SELECT id, document_name, applicant_name, uploaded_at FROM notifications ORDER BY id`, (err, notifications) => {
        if (err) {
          console.error('❌ Error fetching notifications:', err.message);
          reject(err);
          return;
        }
        
        console.log(`📋 Found ${notifications.length} notifications to check:`);
        
        if (notifications.length === 0) {
          console.log('   No notifications found.');
          resolve();
          return;
        }
        
        const now = new Date();
        const oldNotifications = [];
        
        notifications.forEach((notification, index) => {
          const uploadedAt = new Date(notification.uploaded_at);
          const timeDiff = Math.abs(now - uploadedAt);
          const hoursAgo = Math.floor(timeDiff / (1000 * 60 * 60));
          
          console.log(`   ${index + 1}. ${notification.document_name}`);
          console.log(`      Uploaded: ${notification.uploaded_at}`);
          console.log(`      Hours ago: ${hoursAgo}`);
          
          // Consider notifications older than 1 hour as potentially problematic
          if (hoursAgo > 1) {
            oldNotifications.push({
              ...notification,
              hoursAgo
            });
          }
          console.log('');
        });
        
        if (oldNotifications.length === 0) {
          console.log('✅ All notifications have recent timestamps (within 1 hour).');
          console.log('💡 No timestamp fixes needed.');
          resolve();
          return;
        }
        
        console.log(`⚠️  Found ${oldNotifications.length} notifications with old timestamps:`);
        oldNotifications.forEach((notification, index) => {
          console.log(`   ${index + 1}. ${notification.document_name} - ${notification.hoursAgo}h ago`);
        });
        
        console.log('\n🤔 Options for handling old notifications:');
        console.log('1. Leave them as-is (they represent historical upload times)');
        console.log('2. Update them to current time (if they should appear as "new")');
        console.log('3. Delete them and let new uploads create fresh notifications');
        
        console.log('\n💡 Recommendation: Leave historical notifications as-is');
        console.log('   • They accurately represent when documents were uploaded');
        console.log('   • New notifications will use the fixed timestamp logic');
        console.log('   • Users can see the actual upload timeline');
        
        console.log('\n🔧 If you want to update old notifications to current time:');
        console.log('   Uncomment the update code below and run this script again');
        
        // OPTIONAL: Uncomment this section to update old notifications to current time
        /*
        console.log('\n🔄 Updating old notifications to current time...');
        const currentTimestamp = new Date().toISOString();
        let updatedCount = 0;
        
        const updatePromises = oldNotifications.map(notification => {
          return new Promise((resolveUpdate, rejectUpdate) => {
            db.run(
              `UPDATE notifications SET uploaded_at = ? WHERE id = ?`,
              [currentTimestamp, notification.id],
              function(err) {
                if (err) {
                  console.error(`❌ Error updating notification ${notification.id}:`, err.message);
                  rejectUpdate(err);
                } else {
                  updatedCount++;
                  console.log(`✅ Updated notification ${notification.id}: ${notification.document_name}`);
                  resolveUpdate();
                }
              }
            );
          });
        });
        
        Promise.all(updatePromises)
          .then(() => {
            console.log(`\n✅ Updated ${updatedCount} notifications to current time`);
            console.log(`🕒 New timestamp: ${currentTimestamp}`);
            resolve();
          })
          .catch(reject);
        */
        
        // For now, just resolve without updating
        resolve();
      });
    });
  });
}

// Execute fix
fixExistingNotificationTimestamps()
  .then(() => {
    console.log('\n✅ Notification timestamp fix completed');
    console.log('\n🎯 Summary:');
    console.log('✅ createNotification() function updated to use current timestamp');
    console.log('✅ New notifications will show correct "now" time');
    console.log('✅ Existing notifications preserve their historical timestamps');
    console.log('✅ No more "8h ago" issue for new notifications');
    
    console.log('\n📱 Test Instructions:');
    console.log('1. Upload a new document in LDIS');
    console.log('2. Check the notifications panel');
    console.log('3. New notification should show "now" or "1 minute ago"');
    console.log('4. Refresh the page - timestamp should remain current');
    
    console.log('\n🔧 Technical Details:');
    console.log('• Function: createNotification() in src/lib/database.ts');
    console.log('• Change: Added explicit uploaded_at timestamp');
    console.log('• Value: new Date().toISOString()');
    console.log('• Impact: All new notifications use current time');
    
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('\n🔒 Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Fix failed:', error.message);
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
