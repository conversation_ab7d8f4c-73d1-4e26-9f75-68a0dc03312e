#!/usr/bin/env node

/**
 * Migration script to add 'code' columns to documents and archives tables
 * - Adds 'code' column to documents table (unique identifier for each document)
 * - Adds 'code' column to archives table (references the document code)
 */

const sqlite3 = require("sqlite3").verbose();
const path = require("path");

// Database path
const dbPath = path.join(__dirname, "..", "data", "ldis.db");

console.log("🔄 Starting migration to add code columns...");
console.log(`📁 Database path: ${dbPath}`);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error("❌ Error opening database:", err.message);
    process.exit(1);
  }
  console.log("✅ Connected to SQLite database");
});

// Run the migration
async function runMigration() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Step 1: Check if documents table already has code column
      console.log("🔍 Checking documents table structure...");
      db.all("PRAGMA table_info(documents)", (err, documentsInfo) => {
        if (err) {
          console.error(
            "❌ Error checking documents table structure:",
            err.message
          );
          reject(err);
          return;
        }

        const hasDocumentsCode = documentsInfo.some(
          (col) => col.name === "code"
        );

        if (hasDocumentsCode) {
          console.log("ℹ️  Code column already exists in documents table");
          // Skip to archives table
          handleArchivesTable(resolve, reject);
        } else {
          // Need to recreate documents table with code column
          recreateDocumentsTable(resolve, reject);
        }
      });
    });
  });
}

// Function to recreate documents table with code column
function recreateDocumentsTable(resolve, reject) {
  console.log("📝 Recreating documents table with code column...");

  // Step 1: Get existing data
  db.all("SELECT * FROM documents", (err, existingDocuments) => {
    if (err) {
      console.error("❌ Error fetching existing documents:", err.message);
      reject(err);
      return;
    }

    console.log(`📊 Found ${existingDocuments.length} existing documents`);

    // Step 2: Create new documents table with code column
    db.run(
      `
      CREATE TABLE documents_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        document_name TEXT NOT NULL,
        applicant_name TEXT NOT NULL,
        uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        document_data BLOB,
        status TEXT DEFAULT 'pending',
        approved_at DATETIME,
        user_id INTEGER NOT NULL,
        code TEXT UNIQUE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `,
      (err) => {
        if (err) {
          console.error("❌ Error creating new documents table:", err.message);
          reject(err);
          return;
        }

        console.log("✅ New documents table created");

        // Step 3: Copy existing data and generate codes
        if (existingDocuments.length === 0) {
          // No existing data, just rename table
          renameDocumentsTable(resolve, reject);
        } else {
          copyDocumentsData(existingDocuments, resolve, reject);
        }
      }
    );
  });
}

// Function to copy documents data with generated codes
function copyDocumentsData(existingDocuments, resolve, reject) {
  console.log("📋 Copying documents data with generated codes...");

  let completed = 0;
  const total = existingDocuments.length;

  existingDocuments.forEach((doc, index) => {
    // Generate a unique code
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, "");
    const sequence = String(index + 1).padStart(4, "0");
    const code = `DOC-${date}-${sequence}`;

    db.run(
      `
      INSERT INTO documents_new (id, document_name, applicant_name, uploaded_at, document_data, status, approved_at, user_id, code)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `,
      [
        doc.id,
        doc.document_name,
        doc.applicant_name,
        doc.uploaded_at,
        doc.document_data,
        doc.status,
        doc.approved_at,
        doc.user_id,
        code,
      ],
      (err) => {
        if (err) {
          console.error(`❌ Error copying document ${doc.id}:`, err.message);
          reject(err);
          return;
        }

        completed++;
        console.log(`✅ Copied document ${doc.id} with code ${code}`);

        if (completed === total) {
          console.log("✅ All documents copied with codes");
          renameDocumentsTable(resolve, reject);
        }
      }
    );
  });
}

// Function to rename tables
function renameDocumentsTable(resolve, reject) {
  console.log("🔄 Replacing old documents table...");

  // Drop old table and rename new one
  db.run("DROP TABLE documents", (err) => {
    if (err) {
      console.error("❌ Error dropping old documents table:", err.message);
      reject(err);
      return;
    }

    db.run("ALTER TABLE documents_new RENAME TO documents", (err) => {
      if (err) {
        console.error("❌ Error renaming new documents table:", err.message);
        reject(err);
        return;
      }

      console.log("✅ Documents table updated successfully");
      handleArchivesTable(resolve, reject);
    });
  });
}

// Function to handle archives table
function handleArchivesTable(resolve, reject) {
  console.log("📝 Adding code column to archives table...");
  db.run(
    `
    ALTER TABLE archives ADD COLUMN code TEXT
  `,
    (err) => {
      if (err) {
        // Check if column already exists
        if (err.message.includes("duplicate column name")) {
          console.log("ℹ️  Code column already exists in archives table");
        } else {
          console.error(
            "❌ Error adding code column to archives table:",
            err.message
          );
          reject(err);
          return;
        }
      } else {
        console.log("✅ Code column added to archives table");
      }

      // Verify the migration
      verifyMigration(resolve, reject);
    }
  );
}

// Verify the migration was successful
function verifyMigration(resolve, reject) {
  console.log("🔍 Verifying migration...");

  // Check documents table structure
  db.all("PRAGMA table_info(documents)", (err, documentsInfo) => {
    if (err) {
      console.error(
        "❌ Error checking documents table structure:",
        err.message
      );
      reject(err);
      return;
    }

    const hasDocumentsCode = documentsInfo.some((col) => col.name === "code");
    if (!hasDocumentsCode) {
      console.log(
        "❌ Migration failed: code column not found in documents table"
      );
      reject(new Error("Documents table migration verification failed"));
      return;
    }

    // Check archives table structure
    db.all("PRAGMA table_info(archives)", (err, archivesInfo) => {
      if (err) {
        console.error(
          "❌ Error checking archives table structure:",
          err.message
        );
        reject(err);
        return;
      }

      const hasArchivesCode = archivesInfo.some((col) => col.name === "code");
      if (!hasArchivesCode) {
        console.log(
          "❌ Migration failed: code column not found in archives table"
        );
        reject(new Error("Archives table migration verification failed"));
        return;
      }

      console.log("✅ Migration verified successfully!");
      console.log("\n📋 Updated table structures:");
      console.log("Documents table columns:");
      documentsInfo.forEach((col) => {
        const unique = col.name === "code" ? " (UNIQUE)" : "";
        console.log(`   ${col.name}: ${col.type}${unique}`);
      });

      console.log("\nArchives table columns:");
      archivesInfo.forEach((col) => {
        console.log(`   ${col.name}: ${col.type}`);
      });

      resolve();
    });
  });
}

// Execute migration
runMigration()
  .then(() => {
    console.log("\n🎉 Migration completed successfully!");
    console.log("✅ Added code column to documents table (unique)");
    console.log(
      "✅ Added code column to archives table (references document code)"
    );
    console.log("✅ Generated codes for existing documents");

    db.close((err) => {
      if (err) {
        console.error("❌ Error closing database:", err.message);
      } else {
        console.log("✅ Database connection closed");
      }
    });
  })
  .catch((error) => {
    console.error("\n❌ Migration failed:", error.message);

    db.close((err) => {
      if (err) {
        console.error("❌ Error closing database:", err.message);
      }
      process.exit(1);
    });
  });
