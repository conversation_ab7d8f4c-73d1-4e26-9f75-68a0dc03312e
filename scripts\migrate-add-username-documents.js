const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'ldis.db');

// Connect to database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error connecting to database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Enable foreign keys
db.run('PRAGMA foreign_keys = ON');

console.log('🚀 Starting migration to add username field to documents table...');

// Run the migration
async function runMigration() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Step 1: Create new documents table with username column
      console.log('📝 Creating new documents table structure...');
      db.run(`
        CREATE TABLE IF NOT EXISTS documents_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          document_name TEXT NOT NULL,
          applicant_name TEXT NOT NULL,
          uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          document_data BLOB,
          status TEXT DEFAULT 'to review',
          approved_at DATETIME,
          approved_by INTEGER,
          user_id INTEGER NOT NULL,
          code TEXT UNIQUE,
          username TEXT,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `, (err) => {
        if (err) {
          console.error('❌ Error creating new documents table:', err.message);
          reject(err);
          return;
        }
        console.log('✅ New documents table structure created');

        // Step 2: Get all existing documents with user information
        console.log('📋 Fetching existing documents with user information...');
        db.all(`
          SELECT d.*, u.username 
          FROM documents d 
          LEFT JOIN users u ON d.user_id = u.id
        `, (err, documents) => {
          if (err) {
            console.error('❌ Error fetching documents:', err.message);
            reject(err);
            return;
          }

          console.log(`📊 Found ${documents.length} documents to migrate`);

          if (documents.length === 0) {
            // No documents to migrate, just rename tables
            renameDocumentsTable(resolve, reject);
            return;
          }

          // Step 3: Copy documents with username populated
          let completed = 0;
          const total = documents.length;

          documents.forEach((doc) => {
            db.run(`
              INSERT INTO documents_new (
                id, document_name, applicant_name, uploaded_at, document_data, 
                status, approved_at, approved_by, user_id, code, username
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              doc.id,
              doc.document_name,
              doc.applicant_name,
              doc.uploaded_at,
              doc.document_data,
              doc.status,
              doc.approved_at,
              doc.approved_by,
              doc.user_id,
              doc.code,
              doc.username || null // Use username from join, or null if not found
            ], (err) => {
              if (err) {
                console.error(`❌ Error copying document ${doc.id}:`, err.message);
                reject(err);
                return;
              }

              completed++;
              console.log(`✅ Copied document ${doc.id} with username: ${doc.username || 'null'}`);

              if (completed === total) {
                console.log('✅ All documents copied with usernames');
                renameDocumentsTable(resolve, reject);
              }
            });
          });
        });
      });
    });
  });
}

// Function to rename tables
function renameDocumentsTable(resolve, reject) {
  console.log('🔄 Replacing old documents table...');

  // Drop old table and rename new one
  db.run('DROP TABLE documents', (err) => {
    if (err) {
      console.error('❌ Error dropping old documents table:', err.message);
      reject(err);
      return;
    }

    db.run('ALTER TABLE documents_new RENAME TO documents', (err) => {
      if (err) {
        console.error('❌ Error renaming new documents table:', err.message);
        reject(err);
        return;
      }

      console.log('✅ Documents table updated successfully');
      resolve();
    });
  });
}

// Execute migration
runMigration()
  .then(() => {
    console.log('🎉 Migration completed successfully!');
    console.log('📊 Summary of changes:');
    console.log('   - Added username TEXT column to documents table');
    console.log('   - Populated username field for existing documents based on user_id');
    console.log('   - Preserved all existing document data and relationships');
    
    // Close database connection
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('✅ Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Migration failed:', error);
    
    // Close database connection
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
