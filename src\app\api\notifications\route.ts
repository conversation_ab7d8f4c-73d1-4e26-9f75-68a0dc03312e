import { NextResponse } from 'next/server';
import { getAllNotifications, getUnreadNotificationCount } from '@/lib/database';

/**
 * GET /api/notifications - Get all notifications with unread count
 */
export async function GET() {
  try {
    const notifications = await getAllNotifications();
    
    // Get unread count (assuming single user system, use first user)
    let unreadCount = 0;
    if (notifications.length > 0) {
      const userId = notifications[0].user_id;
      unreadCount = await getUnreadNotificationCount(userId);
    }

    return NextResponse.json({
      notifications,
      unreadCount,
      count: notifications.length
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}
