#!/usr/bin/env node

/**
 * Database test script for LDIS Documents Table
 * This script tests the documents table functionality
 */

require("ts-node/register");

async function testDocumentsDatabase() {
  try {
    console.log("🧪 Testing LDIS Documents Database...\n");

    // Import the database module
    const {
      initializeDatabase,
      createDocument,
      getDocumentById,
      getDocumentsByUserId,
      getAllDocuments,
      updateDocumentStatus,
      updateDocumentArchiveStatus,
      deleteDocument,
      getDocumentsWithUserInfo,
      getNonArchivedDocuments,
      getArchivedDocuments,
      getAllUsers,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize the database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Check if users exist
    console.log("👥 Checking users...");
    const users = await getAllUsers();
    console.log(`Found ${users.length} users`);
    
    if (users.length === 0) {
      console.log("⚠️ No users found. You need to create a user first.");
      console.log("Run: node scripts/test-db.js\n");
      await closeDatabase();
      return;
    }

    const testUserId = users[0].id;
    console.log(`Using user ID: ${testUserId} (${users[0].username})\n`);

    // Test creating a document
    console.log("📄 Creating test document...");
    const testDocumentData = Buffer.from("This is test document content", "utf-8");
    
    const documentId = await createDocument(
      "Birth Certificate Application",
      "John Doe",
      testDocumentData,
      "pending",
      testUserId,
      false
    );
    console.log(`✅ Document created with ID: ${documentId}\n`);

    // Test getting document by ID
    console.log("🔍 Getting document by ID...");
    const document = await getDocumentById(documentId);
    if (document) {
      console.log(`✅ Document found: ${document.document_name}`);
      console.log(`   Applicant: ${document.applicant_name}`);
      console.log(`   Status: ${document.status}`);
      console.log(`   Archived: ${document.is_archive}`);
      console.log(`   Data size: ${document.document_data?.length || 0} bytes\n`);
    } else {
      console.log("❌ Document not found\n");
    }

    // Test getting documents by user ID
    console.log("👤 Getting documents by user ID...");
    const userDocuments = await getDocumentsByUserId(testUserId);
    console.log(`✅ Found ${userDocuments.length} documents for user\n`);

    // Test updating document status
    console.log("📝 Updating document status...");
    await updateDocumentStatus(documentId, "approved", new Date().toISOString());
    const updatedDocument = await getDocumentById(documentId);
    console.log(`✅ Document status updated to: ${updatedDocument?.status}`);
    console.log(`   Approved at: ${updatedDocument?.approved_at}\n`);

    // Test archiving document
    console.log("📦 Archiving document...");
    await updateDocumentArchiveStatus(documentId, true);
    const archivedDocument = await getDocumentById(documentId);
    console.log(`✅ Document archived: ${archivedDocument?.is_archive}\n`);

    // Test getting non-archived documents
    console.log("📋 Getting non-archived documents...");
    const nonArchivedDocs = await getNonArchivedDocuments();
    console.log(`✅ Found ${nonArchivedDocs.length} non-archived documents\n`);

    // Test getting archived documents
    console.log("🗃️ Getting archived documents...");
    const archivedDocs = await getArchivedDocuments();
    console.log(`✅ Found ${archivedDocs.length} archived documents\n`);

    // Test getting all documents
    console.log("📊 Getting all documents...");
    const allDocuments = await getAllDocuments();
    console.log(`✅ Found ${allDocuments.length} total documents\n`);

    // Test getting documents with user info
    console.log("👥 Getting documents with user info...");
    const documentsWithUserInfo = await getDocumentsWithUserInfo();
    console.log(`✅ Found ${documentsWithUserInfo.length} documents with user info`);
    if (documentsWithUserInfo.length > 0) {
      const doc = documentsWithUserInfo[0];
      console.log(`   Example: ${doc.document_name} by ${doc.username}\n`);
    }

    // Clean up - delete test document
    console.log("🧹 Cleaning up test document...");
    await deleteDocument(documentId);
    const deletedDocument = await getDocumentById(documentId);
    if (!deletedDocument) {
      console.log("✅ Test document deleted successfully\n");
    } else {
      console.log("❌ Failed to delete test document\n");
    }

    console.log("🎉 All documents database tests completed successfully!");
    console.log("\nDocuments table features tested:");
    console.log("✅ Create document");
    console.log("✅ Get document by ID");
    console.log("✅ Get documents by user ID");
    console.log("✅ Update document status");
    console.log("✅ Update archive status");
    console.log("✅ Get non-archived documents");
    console.log("✅ Get archived documents");
    console.log("✅ Get all documents");
    console.log("✅ Get documents with user info");
    console.log("✅ Delete document\n");

    // Close the database connection
    await closeDatabase();
  } catch (error) {
    console.error("❌ Error testing documents database:", error);
    process.exit(1);
  }
}

// Run the test
testDocumentsDatabase();
