"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Archive,
  RefreshCw,
  RotateCcw,
  FileText,
  User,
  Calendar,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import {
  SearchSortControls,
  type SortOption,
  type FilterOption,
} from "@/components/search-sort-controls";
import { searchFilterSort, createFilterOptions } from "@/lib/search-sort-utils";

interface ArchiveItem {
  id: number;
  document_name: string;
  applicant_name: string;
  uploaded_at: string;
  document_data?: Buffer;
  user_id: number;
  document_id: number;
  approved_at?: string;
  approved_by?: number;
  username?: string;
  approved_by_username?: string;
}

interface ArchivesResponse {
  archives: ArchiveItem[];
  count: number;
}

export default function ArchivesPage() {
  const [allArchives, setAllArchives] = useState<ArchiveItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [restoringArchives, setRestoringArchives] = useState<Set<number>>(
    new Set()
  );
  const [error, setError] = useState<string>("");

  // Search and sort state
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("uploaded_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  // Sort and filter options
  const sortOptions: SortOption[] = [
    { value: "uploaded_at", label: "Archived Date", field: "uploaded_at" },
    { value: "document_name", label: "Document Name", field: "document_name" },
    {
      value: "applicant_name",
      label: "Applicant Name",
      field: "applicant_name",
    },
    { value: "approved_at", label: "Approved Date", field: "approved_at" },
    {
      value: "approved_by_username",
      label: "Archived By",
      field: "approved_by_username",
    },
  ];

  // Generate filter options dynamically from data
  const filterOptions: FilterOption[] = useMemo(() => {
    const archivedByOptions = createFilterOptions(
      allArchives,
      "approved_by_username",
      (value) => value || "Unknown"
    );

    return [
      ...archivedByOptions.map((option) => ({
        ...option,
        label: `Archived by: ${option.label}`,
      })),
    ];
  }, [allArchives]);

  // Apply search, filter, and sort
  const archives = useMemo(() => {
    const filters =
      activeFilters.length > 0
        ? [{ field: "approved_by_username", values: activeFilters }]
        : [];

    return searchFilterSort(allArchives, {
      searchTerm,
      searchFields: ["document_name", "applicant_name", "approved_by_username"],
      filters,
      sortField: sortBy,
      sortOrder,
    });
  }, [allArchives, searchTerm, activeFilters, sortBy, sortOrder]);

  // Search and sort handlers
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  const handleSortChange = (field: string, order: "asc" | "desc") => {
    setSortBy(field);
    setSortOrder(order);
  };

  const handleFilterChange = (filters: string[]) => {
    setActiveFilters(filters);
  };

  const fetchArchives = async () => {
    try {
      const response = await fetch("/api/archives");
      if (!response.ok) {
        throw new Error("Failed to fetch archives");
      }
      const data: ArchivesResponse = await response.json();
      setAllArchives(data.archives);
      setError("");
    } catch (err) {
      console.error("Error fetching archives:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
      toast.error("Failed to load archives");
    }
  };

  useEffect(() => {
    const loadArchives = async () => {
      setLoading(true);
      await fetchArchives();
      setLoading(false);
    };

    loadArchives();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchArchives();
    setRefreshing(false);
  };

  const handleRestoreArchive = async (archive: ArchiveItem) => {
    setRestoringArchives((prev) => new Set(prev).add(archive.id));

    try {
      const response = await fetch(`/api/archives/${archive.id}/restore`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to restore archive");
      }

      toast.success("Archive restored successfully");
      await fetchArchives(); // Refresh the list
    } catch (error) {
      console.error("Error restoring archive:", error);
      toast.error("Failed to restore archive");
    } finally {
      setRestoringArchives((prev) => {
        const newSet = new Set(prev);
        newSet.delete(archive.id);
        return newSet;
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Archive className="h-5 w-5" />
                Archives
              </CardTitle>
              <CardDescription>
                Manage archived documents. Showing {archives.length} of{" "}
                {allArchives.length} archived document
                {allArchives.length !== 1 ? "s" : ""}. The "Archived By" column
                shows who archived the document.
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Search and Sort Controls */}
          <SearchSortControls
            searchValue={searchTerm}
            onSearchChange={handleSearchChange}
            sortOptions={sortOptions}
            sortBy={sortBy}
            sortOrder={sortOrder}
            onSortChange={handleSortChange}
            filterOptions={filterOptions}
            activeFilters={activeFilters}
            onFilterChange={handleFilterChange}
            placeholder="Search archives by document, applicant, or archived by..."
            className="mb-6"
          />

          {allArchives.length === 0 ? (
            <div className="text-center py-12">
              <Archive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground mb-2">
                No archived documents
              </h3>
              <p className="text-sm text-muted-foreground">
                Archived documents will appear here when you archive approved
                documents.
              </p>
            </div>
          ) : archives.length === 0 ? (
            <div className="text-center py-12">
              <Archive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground mb-2">
                No matching archives
              </h3>
              <p className="text-sm text-muted-foreground">
                Try adjusting your search terms or filters.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Document Name</TableHead>
                    <TableHead>Applicant Name</TableHead>
                    <TableHead>Archived Date</TableHead>
                    <TableHead>Approved Date</TableHead>
                    <TableHead title="User who archived this document">
                      Archived By
                    </TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {archives.map((archive) => (
                    <TableRow key={archive.id}>
                      <TableCell className="font-medium">
                        {archive.document_name}
                      </TableCell>
                      <TableCell>{archive.applicant_name}</TableCell>
                      <TableCell>{formatDate(archive.uploaded_at)}</TableCell>
                      <TableCell>
                        {archive.approved_at ? (
                          formatDate(archive.approved_at)
                        ) : (
                          <Badge variant="secondary">Not recorded</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {archive.approved_by_username ? (
                          <Badge variant="outline" className="text-blue-600">
                            {archive.approved_by_username}
                          </Badge>
                        ) : (
                          <Badge variant="secondary">Unknown</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRestoreArchive(archive)}
                          disabled={restoringArchives.has(archive.id)}
                          className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          title="Restore archive"
                        >
                          {restoringArchives.has(archive.id) ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <RotateCcw className="h-4 w-4" />
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
