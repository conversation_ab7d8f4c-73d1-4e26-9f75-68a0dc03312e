const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'ldis.db');

// Connect to database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error connecting to database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Enable foreign keys
db.run('PRAGMA foreign_keys = ON');

console.log('🚀 Starting migration to remove BLOB storage from saved_files table...');

// Run the migration
async function runMigration() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Step 1: Create new saved_files table without document_data BLOB column
      console.log('📝 Creating new saved_files table structure...');
      db.run(`
        CREATE TABLE IF NOT EXISTS saved_files_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          document_metadata TEXT,
          user_id INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `, (err) => {
        if (err) {
          console.error('❌ Error creating new saved_files table:', err.message);
          reject(err);
          return;
        }
        console.log('✅ New saved_files table structure created');

        // Step 2: Copy data from old table to new table (excluding document_data column)
        console.log('📋 Copying data from old table to new table...');
        db.run(`
          INSERT INTO saved_files_new (id, document_metadata, user_id, created_at)
          SELECT id, document_metadata, user_id, created_at
          FROM saved_files
        `, (err) => {
          if (err) {
            console.error('❌ Error copying data:', err.message);
            reject(err);
            return;
          }
          console.log('✅ Data copied successfully');

          // Step 3: Drop old table and rename new one
          console.log('🔄 Replacing old saved_files table...');
          db.run('DROP TABLE saved_files', (err) => {
            if (err) {
              console.error('❌ Error dropping old saved_files table:', err.message);
              reject(err);
              return;
            }

            db.run('ALTER TABLE saved_files_new RENAME TO saved_files', (err) => {
              if (err) {
                console.error('❌ Error renaming new saved_files table:', err.message);
                reject(err);
                return;
              }

              console.log('✅ Saved_files table updated successfully');
              resolve();
            });
          });
        });
      });
    });
  });
}

// Execute migration
runMigration()
  .then(() => {
    console.log('🎉 Migration completed successfully!');
    console.log('📊 Summary of changes:');
    console.log('   - Removed document_data BLOB column from saved_files table');
    console.log('   - Preserved document_metadata, user_id, and created_at columns');
    console.log('   - All existing metadata preserved');
    
    // Close database connection
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('✅ Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Migration failed:', error);
    
    // Close database connection
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
