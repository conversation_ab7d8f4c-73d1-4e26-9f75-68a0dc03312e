# Search and Sort Features Documentation

## Overview

The LDIS system now includes comprehensive search and sort functionality for both the **Notifications** and **Archives** pages. This feature allows users to efficiently find and organize documents using multiple criteria.

## Features

### 🔍 **Search Functionality**
- **Real-time search** across multiple fields
- **Case-insensitive** matching
- **Partial text matching** (substring search)
- **Clear search** button for easy reset
- **Search summary** showing active search terms

### 📊 **Sort Functionality**
- **Multiple sort fields** available for each page
- **Ascending/Descending** order toggle
- **Visual sort indicators** (up/down arrows)
- **Persistent sort state** during session

### 🔽 **Filter Functionality**
- **Dynamic filter options** generated from data
- **Multiple filter selection** support
- **Active filter badges** with individual removal
- **Clear all filters** option
- **Filter count indicator**

### 🔄 **Combined Operations**
- **Search + Filter + Sort** work together seamlessly
- **Real-time updates** as you type or change settings
- **Performance optimized** with React useMemo
- **Responsive design** for mobile and desktop

## Page-Specific Features

### 📋 **Notifications Page**

#### Search Fields:
- Document Name
- Applicant Name

#### Sort Options:
- Upload Date (default: newest first)
- Document Name (alphabetical)
- Applicant Name (alphabetical)
- Read Status (unread first)

#### Filter Options:
- **Read Status**: Unread, Read

#### Results Display:
- Shows "X of Y notifications"
- Empty state for no matches
- Maintains bulk selection functionality

### 📁 **Archives Page**

#### Search Fields:
- Document Name
- Applicant Name
- Archived By (username)

#### Sort Options:
- Archived Date (default: newest first)
- Document Name (alphabetical)
- Applicant Name (alphabetical)
- Approved Date
- Archived By (username)

#### Filter Options:
- **Archived By**: Dynamic list of users who archived documents

#### Results Display:
- Shows "X of Y archived documents"
- Empty state for no matches
- Maintains restore functionality

## Technical Implementation

### 🏗️ **Architecture**

```
src/
├── components/
│   └── search-sort-controls.tsx    # Reusable search/sort UI component
├── lib/
│   └── search-sort-utils.ts        # Core search/sort logic utilities
├── app/admin/
│   ├── notifications/page.tsx      # Notifications with search/sort
│   └── archives/page.tsx           # Archives with search/sort
└── scripts/
    └── test-search-sort-functionality.js  # Test suite
```

### 🧩 **Components**

#### `SearchSortControls`
Reusable component providing:
- Search input with clear button
- Sort dropdown and order toggle
- Filter dropdown with multi-select
- Active filter badges
- Responsive layout

#### `search-sort-utils.ts`
Utility functions for:
- Text search across multiple fields
- Multi-criteria filtering
- Field-based sorting (with type handling)
- Combined operations
- Filter option generation

### 🔧 **Key Functions**

```typescript
// Main search/filter/sort function
searchFilterSort(items, {
  searchTerm: string,
  searchFields: string[],
  filters: { field: string, values: string[] }[],
  sortField: string,
  sortOrder: "asc" | "desc"
})

// Generate filter options from data
createFilterOptions(items, field, labelFormatter?)

// Get unique values for a field
getUniqueFieldValues(items, field)
```

## Usage Examples

### Basic Search
```typescript
const results = searchFilterSort(notifications, {
  searchTerm: "medical",
  searchFields: ["document_name", "applicant_name"]
});
```

### Combined Operations
```typescript
const results = searchFilterSort(archives, {
  searchTerm: "certificate",
  searchFields: ["document_name", "applicant_name"],
  filters: [{ field: "approved_by_username", values: ["admin"] }],
  sortField: "uploaded_at",
  sortOrder: "desc"
});
```

## Performance Considerations

### ⚡ **Optimizations**
- **React.useMemo** for expensive operations
- **Debounced search** (can be enabled if needed)
- **Efficient filtering** with early returns
- **Memory-conscious** sorting (creates new arrays)

### 📊 **Scalability**
- Handles hundreds of items efficiently
- Can be extended for server-side search/sort
- Supports pagination (future enhancement)
- Minimal re-renders with proper dependencies

## User Experience

### 🎯 **Intuitive Design**
- **Familiar search patterns** (Google-like search)
- **Clear visual feedback** for active filters
- **Responsive controls** for all screen sizes
- **Keyboard navigation** support

### 📱 **Mobile-Friendly**
- **Stacked layout** on small screens
- **Touch-friendly** controls
- **Readable text** and proper spacing
- **Swipe-friendly** filter badges

## Testing

### 🧪 **Test Coverage**
- **Unit tests** for all utility functions
- **Edge case handling** (empty data, invalid fields)
- **Performance tests** with large datasets
- **Integration tests** with React components

### 🔍 **Test Script**
Run the test suite:
```bash
node scripts/test-search-sort-functionality.js
```

## Future Enhancements

### 🚀 **Planned Features**
- **Advanced search operators** (AND, OR, NOT)
- **Date range filtering** for timestamps
- **Saved search presets** for common queries
- **Export filtered results** to CSV/PDF
- **Search history** and suggestions

### 🔧 **Technical Improvements**
- **Server-side search** for large datasets
- **Full-text search** with highlighting
- **Fuzzy matching** for typo tolerance
- **Search analytics** and usage tracking

## Troubleshooting

### ❓ **Common Issues**

**Search not working:**
- Check if `searchFields` array is properly defined
- Verify field names match data structure
- Ensure search term is not empty string

**Sort not working:**
- Verify `sortField` exists in data
- Check for null/undefined values in sort field
- Ensure proper data types for comparison

**Filters not showing:**
- Check if `filterOptions` array is populated
- Verify filter field has unique values
- Ensure data is loaded before generating options

### 🐛 **Debug Tips**
- Use browser dev tools to inspect data structure
- Check console for any JavaScript errors
- Verify React component re-renders with proper dependencies
- Test with sample data to isolate issues

## Conclusion

The search and sort functionality significantly enhances the LDIS user experience by providing powerful tools to find and organize documents efficiently. The implementation is scalable, performant, and user-friendly, making it easy for users to manage large numbers of notifications and archived documents.
