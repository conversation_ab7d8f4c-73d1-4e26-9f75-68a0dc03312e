const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🔍 Testing QR code fix verification...');
console.log('📍 Database path:', dbPath);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Test the QR code fix
async function testQRCodeFix() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n🔍 Checking approved documents that should show QR codes...');
      
      // Get all approved documents
      db.all(`SELECT id, document_name, status, approved_at, approved_by FROM documents WHERE status = 'approved' ORDER BY id`, (err, docs) => {
        if (err) {
          console.error('❌ Error fetching approved documents:', err.message);
          reject(err);
          return;
        }
        
        console.log(`📄 Found ${docs.length} approved documents:`);
        
        if (docs.length === 0) {
          console.log('⚠️  No approved documents found. QR codes will only show for approved documents.');
          console.log('💡 To test QR codes:');
          console.log('   1. Go to the admin panel');
          console.log('   2. Navigate to a document');
          console.log('   3. Click "Edit" and approve the document');
          console.log('   4. The QR code should appear in the preview');
          console.log('   5. Refresh the page - the QR code should still be there');
        } else {
          console.log('\n📋 Approved documents (should show QR codes):');
          docs.forEach((doc, index) => {
            console.log(`${index + 1}. ${doc.document_name} (ID: ${doc.id})`);
            console.log(`   Status: ${doc.status}`);
            console.log(`   Approved: ${doc.approved_at ? new Date(doc.approved_at).toLocaleString() : 'N/A'}`);
            console.log(`   Approved By: ${doc.approved_by || 'N/A'}`);
            console.log('');
          });
          
          console.log('🎯 QR Code Test Instructions:');
          console.log('1. Open the LDIS application in your browser');
          console.log('2. Navigate to Administration > Documents');
          console.log('3. Click on any of the approved documents listed above');
          console.log('4. You should see a QR code in the bottom-left of the document preview');
          console.log('5. Refresh the page (F5) - the QR code should still be visible');
          console.log('6. If the QR code disappears after refresh, the bug is still present');
          console.log('7. If the QR code persists after refresh, the bug is fixed! ✅');
        }
        
        console.log('\n🔧 Technical Details:');
        console.log('- QR codes are generated with the text "test"');
        console.log('- QR codes appear in the bottom-left corner (60x60px)');
        console.log('- QR codes only show for documents with status = "approved"');
        console.log('- The fix ensures QR codes check the real database status, not just local state');
        
        resolve();
      });
    });
  });
}

// Execute test
testQRCodeFix()
  .then(() => {
    console.log('\n✅ QR code fix verification completed');
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('🔒 Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Test failed:', error.message);
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
