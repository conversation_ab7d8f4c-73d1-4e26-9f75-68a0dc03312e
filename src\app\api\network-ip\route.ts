import { NextResponse } from 'next/server';
import { networkInterfaces } from 'os';

/**
 * GET /api/network-ip - Get the local network IP address for QR code generation
 */
export async function GET() {
  try {
    const interfaces = networkInterfaces();
    let localIP = 'localhost';
    let baseURL = 'http://localhost:3000';

    // Find the first non-internal IPv4 address
    for (const interfaceName in interfaces) {
      const networkInterface = interfaces[interfaceName];
      if (networkInterface) {
        for (const net of networkInterface) {
          // Skip internal (loopback) and non-IPv4 addresses
          if (net.family === 'IPv4' && !net.internal) {
            localIP = net.address;
            baseURL = `http://${localIP}:3000`;
            break;
          }
        }
        if (localIP !== 'localhost') break;
      }
    }

    // If we couldn't find a network IP, try to get it from environment or use localhost
    if (localIP === 'localhost') {
      // Check if we have a custom base URL in environment
      const envBaseURL = process.env.NEXT_PUBLIC_BASE_URL;
      if (envBaseURL) {
        baseURL = envBaseURL;
        // Extract IP from URL if possible
        const urlMatch = envBaseURL.match(/https?:\/\/([^:\/]+)/);
        if (urlMatch && urlMatch[1] !== 'localhost') {
          localIP = urlMatch[1];
        }
      }
    }

    return NextResponse.json({
      success: true,
      localIP,
      baseURL,
      message: localIP === 'localhost' 
        ? 'Using localhost - mobile devices may not be able to access this URL'
        : `Network IP detected: ${localIP}`
    });

  } catch (error) {
    console.error('Error detecting network IP:', error);
    
    // Fallback to localhost
    return NextResponse.json({
      success: true,
      localIP: 'localhost',
      baseURL: 'http://localhost:3000',
      message: 'Could not detect network IP, using localhost as fallback',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
