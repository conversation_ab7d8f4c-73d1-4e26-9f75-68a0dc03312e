#!/usr/bin/env node

/**
 * Migration script to fix archives table foreign key constraint
 * This script recreates the archives table without the document_id foreign key constraint
 * that was causing archives to be deleted when the original document is deleted
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🔄 Starting migration to fix archive foreign key constraint...');
console.log(`📁 Database path: ${dbPath}`);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Run the migration
async function runMigration() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Step 1: Check if archives table exists and get current data
      console.log('🔍 Checking current archives table...');
      db.all('SELECT * FROM archives', (err, existingArchives) => {
        if (err) {
          console.log('ℹ️ Archives table does not exist yet, creating new one...');
          existingArchives = [];
        } else {
          console.log(`✅ Found ${existingArchives.length} existing archives`);
        }

        // Step 2: Drop existing archives table if it exists
        console.log('🗑️ Dropping existing archives table...');
        db.run('DROP TABLE IF EXISTS archives', (err) => {
          if (err) {
            console.error('❌ Error dropping archives table:', err.message);
            reject(err);
            return;
          }
          console.log('✅ Existing archives table dropped');

          // Step 3: Create new archives table without document_id foreign key constraint
          console.log('📝 Creating new archives table structure...');
          db.run(`
            CREATE TABLE archives (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              document_name TEXT NOT NULL,
              applicant_name TEXT NOT NULL,
              uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              document_data BLOB,
              user_id INTEGER NOT NULL,
              document_id INTEGER NOT NULL,
              approved_at DATETIME,
              approved_by INTEGER,
              FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
              FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
            )
          `, (err) => {
            if (err) {
              console.error('❌ Error creating new archives table:', err.message);
              reject(err);
              return;
            }
            console.log('✅ New archives table created without document_id foreign key constraint');

            // Step 4: Restore existing data if any
            if (existingArchives.length > 0) {
              console.log('📋 Restoring existing archive data...');
              
              const insertStmt = db.prepare(`
                INSERT INTO archives (id, document_name, applicant_name, uploaded_at, document_data, user_id, document_id, approved_at, approved_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
              `);

              let insertedCount = 0;
              existingArchives.forEach((archive) => {
                insertStmt.run([
                  archive.id,
                  archive.document_name,
                  archive.applicant_name,
                  archive.uploaded_at,
                  archive.document_data,
                  archive.user_id,
                  archive.document_id,
                  archive.approved_at,
                  archive.approved_by
                ], (err) => {
                  if (err) {
                    console.error('❌ Error inserting archive:', err.message);
                    reject(err);
                    return;
                  }
                  insertedCount++;
                  
                  if (insertedCount === existingArchives.length) {
                    insertStmt.finalize();
                    console.log(`✅ Restored ${insertedCount} archives`);
                    
                    // Step 5: Verify the migration
                    console.log('🔍 Verifying migration...');
                    db.all('PRAGMA foreign_key_list(archives)', (err, foreignKeys) => {
                      if (err) {
                        console.error('❌ Error checking foreign keys:', err.message);
                        reject(err);
                        return;
                      }

                      const hasDocumentIdFK = foreignKeys.some(fk => fk.from === 'document_id');
                      if (hasDocumentIdFK) {
                        console.log('❌ Migration failed: document_id foreign key still exists');
                        reject(new Error('Migration verification failed'));
                      } else {
                        console.log('✅ Migration verified: document_id foreign key successfully removed');
                        
                        // Show current foreign keys
                        console.log('\n📋 Current archives table foreign keys:');
                        foreignKeys.forEach(fk => {
                          console.log(`   ${fk.from} → ${fk.table}(${fk.to}) ON DELETE ${fk.on_delete}`);
                        });
                        
                        resolve();
                      }
                    });
                  }
                });
              });
            } else {
              // No existing data, just verify
              console.log('🔍 Verifying migration...');
              db.all('PRAGMA foreign_key_list(archives)', (err, foreignKeys) => {
                if (err) {
                  console.error('❌ Error checking foreign keys:', err.message);
                  reject(err);
                  return;
                }

                const hasDocumentIdFK = foreignKeys.some(fk => fk.from === 'document_id');
                if (hasDocumentIdFK) {
                  console.log('❌ Migration failed: document_id foreign key still exists');
                  reject(new Error('Migration verification failed'));
                } else {
                  console.log('✅ Migration verified: document_id foreign key successfully removed');
                  
                  // Show current foreign keys
                  console.log('\n📋 Current archives table foreign keys:');
                  foreignKeys.forEach(fk => {
                    console.log(`   ${fk.from} → ${fk.table}(${fk.to}) ON DELETE ${fk.on_delete}`);
                  });
                  
                  resolve();
                }
              });
            }
          });
        });
      });
    });
  });
}

// Execute migration
runMigration()
  .then(() => {
    console.log('\n🎉 Migration completed successfully!');
    console.log('The document_id foreign key constraint has been removed from the archives table.');
    console.log('Archives will no longer be automatically deleted when the original document is deleted.');
    console.log('This enables proper hard archiving functionality.');
    
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      } else {
        console.log('✅ Database connection closed');
      }
    });
  })
  .catch((error) => {
    console.error('\n❌ Migration failed:', error.message);
    
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
