const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { networkInterfaces } = require('os');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🌐 Testing IPv4 QR code generation...');
console.log('📍 Database path:', dbPath);

// Function to get network IP (same logic as the API)
function getNetworkIP() {
  const interfaces = networkInterfaces();
  let localIP = 'localhost';
  let baseURL = 'http://localhost:3000';

  // Find the first non-internal IPv4 address
  for (const interfaceName in interfaces) {
    const networkInterface = interfaces[interfaceName];
    if (networkInterface) {
      for (const net of networkInterface) {
        // Skip internal (loopback) and non-IPv4 addresses
        if (net.family === 'IPv4' && !net.internal) {
          localIP = net.address;
          baseURL = `http://${localIP}:3000`;
          break;
        }
      }
      if (localIP !== 'localhost') break;
    }
  }

  return { localIP, baseURL };
}

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Test the IPv4 QR code functionality
async function testIPv4QR() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n🔍 Checking network configuration...');
      
      // Get network IP
      const { localIP, baseURL } = getNetworkIP();
      
      console.log(`🌐 Network IP detected: ${localIP}`);
      console.log(`🔗 Base URL: ${baseURL}`);
      
      if (localIP === 'localhost') {
        console.log('⚠️  Using localhost - mobile devices may not be able to access validation URLs');
      } else {
        console.log('✅ Network IP detected - mobile devices should be able to access validation URLs');
      }
      
      // Check documents and archives with codes
      db.all(`SELECT id, document_name, applicant_name, status, code, approved_at FROM documents WHERE code IS NOT NULL ORDER BY id`, (err, docs) => {
        if (err) {
          console.error('❌ Error fetching documents:', err.message);
          reject(err);
          return;
        }
        
        db.all(`SELECT id, document_name, applicant_name, status, code, approved_at FROM archives WHERE code IS NOT NULL ORDER BY id`, (err, archives) => {
          if (err) {
            console.error('❌ Error fetching archives:', err.message);
            reject(err);
            return;
          }
          
          const allItems = [...docs, ...archives];
          const approvedItems = allItems.filter(item => item.status === 'approved');
          
          console.log(`\n📄 Found ${allItems.length} documents/archives with codes`);
          console.log(`✅ Found ${approvedItems.length} approved documents/archives (will show QR codes)`);
          
          if (approvedItems.length > 0) {
            console.log('\n🔗 QR Code URLs (using network IP):');
            approvedItems.forEach((item, index) => {
              const validationUrl = `${baseURL}/validate/${item.code}`;
              console.log(`${index + 1}. ${item.document_name} (${item.applicant_name})`);
              console.log(`   Code: ${item.code}`);
              console.log(`   QR URL: ${validationUrl}`);
              console.log('');
            });
            
            console.log('🎯 Testing Instructions:');
            console.log('1. Open the LDIS application in your browser');
            console.log('2. Navigate to Administration > Documents');
            console.log('3. Click on any approved document from the list above');
            console.log('4. The QR code should now contain the network IP URL (not localhost)');
            console.log('5. Scan the QR code with a mobile device connected to the same network');
            console.log('6. The mobile device should be able to access the validation page');
            
            if (localIP !== 'localhost') {
              console.log('\n📱 Mobile Testing:');
              console.log('• Connect your mobile device to the same WiFi network');
              console.log('• Scan any QR code from the approved documents');
              console.log('• The validation page should load successfully on mobile');
              console.log(`• URLs will use ${localIP} instead of localhost`);
            }
          } else {
            console.log('\n⚠️  No approved documents with codes found.');
            console.log('💡 To test IPv4 QR codes:');
            console.log('   1. Create and approve a document');
            console.log('   2. The QR code will use the network IP for mobile access');
          }
          
          console.log('\n🔧 Technical Details:');
          console.log('• QR codes now use /api/network-ip endpoint');
          console.log('• Same logic as QR Access system');
          console.log('• Automatically detects IPv4 address');
          console.log('• Falls back to localhost if no network IP found');
          console.log('• Mobile devices can access validation URLs');
          
          resolve();
        });
      });
    });
  });
}

// Execute test
testIPv4QR()
  .then(() => {
    console.log('\n✅ IPv4 QR code test completed');
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('🔒 Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Test failed:', error.message);
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
