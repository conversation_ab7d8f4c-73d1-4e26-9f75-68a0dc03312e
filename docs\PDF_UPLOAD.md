# PDF Upload System

The Legal Document Issuance System (LDIS) now includes a PDF upload feature that allows users to upload generated PDF documents, extract embedded metadata, and store the information in the documents database.

## Overview

The PDF upload system provides:

- **File Upload Interface** - User-friendly form for uploading PDF documents
- **Metadata Extraction** - Automatic extraction of embedded LDIS metadata from PDFs
- **Database Storage** - Insertion of extracted data into the documents table
- **File Cleanup** - Automatic deletion of uploaded PDF files after processing
- **Error Handling** - Comprehensive validation and error reporting

## Features

### Upload Page (`/upload`)

- Clean, modern interface using Shadcn components
- File validation (PDF only, max 10MB)
- Real-time upload progress and feedback
- Extracted data preview after successful upload
- Form reset functionality

### API Endpoint (`/api/documents/upload`)

- Secure file upload handling
- PDF text extraction using `pdf-parse` library
- Metadata validation and parsing
- Database integration with user authentication
- Temporary file management and cleanup

## How It Works

### 1. File Upload Process

1. User selects a PDF file through the upload form
2. Client-side validation checks file type and size
3. File is uploaded to the server via FormData
4. Server validates the file and creates a temporary copy

### 2. Metadata Extraction

1. PDF text content is extracted using `pdf-parse`
2. System searches for `LDIS_METADATA:` marker in the text
3. JSON metadata is parsed and validated
4. Document information is extracted from the metadata

### 3. Database Storage

1. Current user information is retrieved
2. Document data is stored as JSON in the documents table
3. Record includes document name, applicant name, and form data
4. Status is set to 'uploaded' to distinguish from generated documents

### 4. File Cleanup

1. Temporary PDF file is deleted from the server
2. Only the extracted metadata is retained in the database
3. No PDF files are permanently stored on the server

## File Structure

```
src/app/upload/page.tsx                    # Upload page component
src/app/api/documents/upload/route.ts      # Upload API endpoint
src/lib/pdf-metadata-utils.ts              # Metadata extraction utilities
scripts/test-pdf-upload.js                 # Testing script
docs/PDF_UPLOAD.md                         # This documentation
```

## Database Schema

The extracted data is stored in the `documents` table:

```sql
CREATE TABLE documents (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  document_name TEXT NOT NULL,           -- From metadata.document_name
  applicant_name TEXT NOT NULL,          -- From metadata.applicant_name
  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  document_data BLOB,                    -- JSON string of metadata.document_data
  status TEXT DEFAULT 'uploaded',        -- Set to 'uploaded' for uploaded docs
  approved_at DATETIME,
  user_id INTEGER NOT NULL,
  is_archive BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## Usage Examples

### Uploading a Document

1. Navigate to `/upload` page
2. Click "Choose File" and select a PDF generated by LDIS
3. Click "Upload & Process"
4. View the extracted data in the preview section
5. Document is automatically saved to the database

### API Usage

```javascript
const formData = new FormData();
formData.append('pdfFile', pdfFile);

const response = await fetch('/api/documents/upload', {
  method: 'POST',
  body: formData,
});

const result = await response.json();
```

## Error Handling

The system handles various error scenarios:

- **Invalid File Type**: Only PDF files are accepted
- **File Size Limit**: Maximum 10MB file size
- **Missing Metadata**: PDF must contain LDIS metadata
- **Parse Errors**: Invalid or corrupted PDF files
- **Database Errors**: Connection or insertion failures
- **Authentication**: User must be authenticated

## Security Features

- File type validation (PDF only)
- File size limits (10MB maximum)
- Temporary file cleanup
- User authentication required
- Input sanitization and validation
- No permanent file storage on server

## Testing

### Automated Testing

Run the test script to verify functionality:

```bash
node scripts/test-pdf-upload.js
```

### Manual Testing

1. Start the development server: `pnpm dev`
2. Navigate to `http://localhost:3000/upload`
3. Generate a document using the LDIS system
4. Upload the generated PDF
5. Verify data extraction and database storage

## Technical Notes

### PDF Processing

- Uses `pdf-parse` library for text extraction
- Dynamic import to avoid build-time issues
- Supports all PDF versions compatible with pdf-parse

### Metadata Format

The system expects PDF metadata in this format:

```
LDIS_METADATA:{"document_name":"...","applicant_name":"...","document_data":{...},"generation_info":{...}}
```

### Performance Considerations

- Files are processed synchronously
- Temporary files are cleaned up immediately
- Memory usage is optimized for large files
- Database operations are atomic

## Future Enhancements

Potential improvements for the upload system:

1. **Batch Upload** - Support for multiple PDF files
2. **Progress Tracking** - Real-time upload progress bars
3. **File Preview** - PDF preview before upload
4. **Metadata Editing** - Allow manual metadata correction
5. **Export Features** - Export extracted data to various formats
6. **Audit Logging** - Track all upload activities
7. **File Validation** - Enhanced PDF structure validation

## Troubleshooting

### Common Issues

**Upload fails with "No metadata found"**
- Ensure the PDF was generated by the LDIS system
- Check that metadata is properly embedded

**File size error**
- Reduce PDF file size or increase the limit in the code
- Check available disk space

**Server errors**
- Verify the temp directory exists and is writable
- Check database connectivity
- Review server logs for detailed error information

### Debug Mode

Enable debug logging by checking the browser console and server logs during upload operations.
