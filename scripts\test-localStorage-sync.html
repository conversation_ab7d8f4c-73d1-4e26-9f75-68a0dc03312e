<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>localStorage Sync Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .component {
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 8px;
            flex: 1;
        }
        .component h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .admin-enabled {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .admin-disabled {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>localStorage Synchronization Test</h1>
    <p>This test simulates the admin mode toggle issue and demonstrates the fix.</p>
    
    <div class="container">
        <div class="component">
            <h3>Settings Component</h3>
            <div id="settings-status" class="status admin-disabled">Admin Mode: Disabled</div>
            <button onclick="toggleAdminMode(true)">Enable Admin Mode</button>
            <button onclick="toggleAdminMode(false)">Disable Admin Mode</button>
        </div>
        
        <div class="component">
            <h3>Sidebar Component</h3>
            <div id="sidebar-status" class="status admin-disabled">Admin Mode: Disabled</div>
            <div id="admin-sections" style="display: none;">
                <p>🔧 Admin Dashboard</p>
                <p>📋 Documents</p>
                <p>📁 Templates</p>
            </div>
        </div>
    </div>
    
    <div class="component">
        <h3>Event Log</h3>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        // Simulate the useLocalStorage hook behavior
        let adminMode = false;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateUI() {
            const settingsStatus = document.getElementById('settings-status');
            const sidebarStatus = document.getElementById('sidebar-status');
            const adminSections = document.getElementById('admin-sections');
            
            if (adminMode) {
                settingsStatus.textContent = 'Admin Mode: Enabled';
                settingsStatus.className = 'status admin-enabled';
                sidebarStatus.textContent = 'Admin Mode: Enabled';
                sidebarStatus.className = 'status admin-enabled';
                adminSections.style.display = 'block';
            } else {
                settingsStatus.textContent = 'Admin Mode: Disabled';
                settingsStatus.className = 'status admin-disabled';
                sidebarStatus.textContent = 'Admin Mode: Disabled';
                sidebarStatus.className = 'status admin-disabled';
                adminSections.style.display = 'none';
            }
        }
        
        function toggleAdminMode(enabled) {
            adminMode = enabled;
            localStorage.setItem('ldis-admin-mode', JSON.stringify(enabled));
            
            // Dispatch custom event for same-tab synchronization (our fix!)
            const customEvent = new CustomEvent('localStorageChange', {
                detail: { key: 'ldis-admin-mode', value: enabled }
            });
            window.dispatchEvent(customEvent);
            
            log(`Admin mode ${enabled ? 'enabled' : 'disabled'} from Settings component`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Listen for localStorage changes (our fix!)
        window.addEventListener('localStorageChange', (e) => {
            if (e.detail.key === 'ldis-admin-mode') {
                adminMode = e.detail.value;
                updateUI();
                log(`Sidebar component received admin mode change: ${adminMode ? 'enabled' : 'disabled'}`);
            }
        });
        
        // Listen for storage events from other tabs
        window.addEventListener('storage', (e) => {
            if (e.key === 'ldis-admin-mode' && e.newValue !== null) {
                adminMode = JSON.parse(e.newValue);
                updateUI();
                log(`Received storage event from another tab: ${adminMode ? 'enabled' : 'disabled'}`);
            }
        });
        
        // Initialize
        try {
            const stored = localStorage.getItem('ldis-admin-mode');
            if (stored) {
                adminMode = JSON.parse(stored);
            }
        } catch (error) {
            log('Error reading from localStorage: ' + error.message);
        }
        
        updateUI();
        log('Test initialized. Try toggling admin mode and see how both components update!');
        log('You can also open this page in multiple tabs to test cross-tab synchronization.');
    </script>
</body>
</html>
