import { NextRequest, NextResponse } from 'next/server';
import { getAllUsers, createUser, getUserByUsername } from '@/lib/database';
import bcrypt from 'bcryptjs';

/**
 * POST /api/auth/signup - Create a new user (only if no users exist)
 */
/**
 * Generate a random recovery key
 */
function generateRecoveryKey(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 12; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password, recoveryKey, role } = body;

    // Validate required fields
    if (!username || !password || !role) {
      return NextResponse.json(
        { error: 'Username, password, and role are required' },
        { status: 400 }
      );
    }

    // Validate role
    if (role !== 'admin' && role !== 'regular') {
      return NextResponse.json(
        { error: 'Role must be either "admin" or "regular"' },
        { status: 400 }
      );
    }



    // Check if any admin users already exist (only 1 admin allowed)
    const existingUsers = await getAllUsers();
    const adminUsers = existingUsers.filter(user => user.role === 'admin');
    if (role === 'admin' && adminUsers.length > 0) {
      return NextResponse.json(
        { error: 'Admin user already exists. Only one admin user is allowed.' },
        { status: 403 }
      );
    }
    
    // Check if username already exists (redundant check but good practice)
    const existingUser = await getUserByUsername(username);
    if (existingUser) {
      return NextResponse.json(
        { error: 'Username already exists' },
        { status: 409 }
      );
    }
    
    // Validate password strength
    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }
    
    // Generate recovery key if not provided, or validate if provided
    let finalRecoveryKey = recoveryKey;
    if (!recoveryKey || recoveryKey.trim() === '') {
      finalRecoveryKey = generateRecoveryKey();
    } else if (recoveryKey.length < 4) {
      return NextResponse.json(
        { error: 'Recovery key must be at least 4 characters long' },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create the user
    const userId = await createUser(username, hashedPassword, finalRecoveryKey, role);
    
    return NextResponse.json(
      {
        message: 'User created successfully',
        user: {
          id: userId,
          username,
          role
        },
        recoveryKey: finalRecoveryKey
      },
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Error during sign up:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
