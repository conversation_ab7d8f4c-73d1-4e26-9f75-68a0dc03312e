#!/usr/bin/env node

/**
 * Database test script for LDIS
 * This script tests the database functionality by creating a test user
 */

require("ts-node/register");

async function testDatabase() {
  try {
    console.log("🧪 Testing LDIS SQLite Database...\n");

    // Import the database module
    const {
      initializeDatabase,
      createUser,
      getUserByUsername,
      getAllUsers,
      closeDatabase,
    } = require("../src/lib/database.ts");

    const bcrypt = require("bcryptjs");

    // Initialize the database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Test creating a user (or use existing one)
    console.log("👤 Creating test user...");
    const testPassword = "testPassword123";
    const hashedPassword = await bcrypt.hash(testPassword, 12);

    let userId;
    try {
      userId = await createUser("testuser", hashedPassword, "recovery123");
      console.log(`✅ Test user created with ID: ${userId}\n`);
    } catch (error) {
      if (error.code === "SQLITE_CONSTRAINT") {
        console.log("ℹ️  Test user already exists, using existing user\n");
        const existingUser = await getUserByUsername("testuser");
        userId = existingUser?.id;
      } else {
        throw error;
      }
    }

    // Test retrieving the user
    console.log("🔍 Retrieving test user...");
    const user = await getUserByUsername("testuser");
    if (user) {
      console.log(`✅ User found: ${user.username} (ID: ${user.id})`);
      console.log(`📅 Created at: ${user.created_at}`);

      // Test password verification
      const passwordMatch = await bcrypt.compare(testPassword, user.password);
      console.log(
        `🔐 Password verification: ${
          passwordMatch ? "✅ Success" : "❌ Failed"
        }\n`
      );
    } else {
      console.log("❌ User not found\n");
    }

    // Test getting all users
    console.log("📋 Getting all users...");
    const allUsers = await getAllUsers();
    console.log(`✅ Found ${allUsers.length} users in database\n`);

    allUsers.forEach((user, index) => {
      console.log(
        `${index + 1}. ${user.username} (ID: ${user.id}) - Created: ${
          user.created_at
        }`
      );
    });

    console.log("\n🎉 All database tests passed successfully!");

    // Close the database connection
    await closeDatabase();
  } catch (error) {
    console.error("❌ Database test failed:", error);
    process.exit(1);
  }
}

// Run the test
testDatabase();
