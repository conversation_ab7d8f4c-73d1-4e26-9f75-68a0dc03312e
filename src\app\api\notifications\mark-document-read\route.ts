import { NextRequest, NextResponse } from 'next/server';
import { markDocumentNotificationsAsRead } from '@/lib/database';

/**
 * POST /api/notifications/mark-document-read - Mark all notifications as read for a specific document
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { documentId } = body;

    if (!documentId || isNaN(parseInt(documentId))) {
      return NextResponse.json(
        { error: 'Valid document ID is required' },
        { status: 400 }
      );
    }

    // Mark all notifications as read for the document
    await markDocumentNotificationsAsRead(parseInt(documentId));

    return NextResponse.json(
      { 
        message: 'Document notifications marked as read',
        documentId: parseInt(documentId)
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error marking document notifications as read:', error);
    return NextResponse.json(
      { error: 'Failed to mark document notifications as read' },
      { status: 500 }
    );
  }
}
