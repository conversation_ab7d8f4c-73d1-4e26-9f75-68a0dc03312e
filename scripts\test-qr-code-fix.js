const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🔍 Testing QR code fix - checking database structure and data...');
console.log('📍 Database path:', dbPath);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Test the database structure and data
async function runTests() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n📊 Checking documents table structure...');
      
      // Check documents table structure
      db.all(`PRAGMA table_info(documents)`, (err, rows) => {
        if (err) {
          console.error('❌ Error checking documents table:', err.message);
          reject(err);
          return;
        }
        
        console.log('📋 Documents table columns:');
        rows.forEach(row => {
          console.log(`  - ${row.name}: ${row.type}${row.notnull ? ' NOT NULL' : ''}${row.dflt_value ? ` DEFAULT ${row.dflt_value}` : ''}`);
        });
        
        const hasApprovedBy = rows.some(row => row.name === 'approved_by');
        console.log(`✅ approved_by column: ${hasApprovedBy ? 'Present' : '❌ Missing'}`);
        
        console.log('\n📊 Checking archives table structure...');
        
        // Check archives table structure
        db.all(`PRAGMA table_info(archives)`, (err, archRows) => {
          if (err) {
            console.error('❌ Error checking archives table:', err.message);
            reject(err);
            return;
          }
          
          console.log('📋 Archives table columns:');
          archRows.forEach(row => {
            console.log(`  - ${row.name}: ${row.type}${row.notnull ? ' NOT NULL' : ''}${row.dflt_value ? ` DEFAULT ${row.dflt_value}` : ''}`);
          });
          
          const hasStatus = archRows.some(row => row.name === 'status');
          console.log(`✅ status column: ${hasStatus ? 'Present' : '❌ Missing'}`);
          
          console.log('\n📊 Checking existing documents...');
          
          // Check existing documents and their status
          db.all(`SELECT id, document_name, status, approved_at, approved_by FROM documents ORDER BY id`, (err, docs) => {
            if (err) {
              console.error('❌ Error fetching documents:', err.message);
              reject(err);
              return;
            }
            
            console.log(`📄 Found ${docs.length} documents:`);
            docs.forEach(doc => {
              console.log(`  - ID: ${doc.id}, Name: ${doc.document_name}, Status: ${doc.status}, Approved: ${doc.approved_at ? 'Yes' : 'No'}, Approved By: ${doc.approved_by || 'N/A'}`);
            });
            
            const approvedDocs = docs.filter(doc => doc.status === 'approved');
            console.log(`\n✅ ${approvedDocs.length} approved documents found`);
            
            if (approvedDocs.length > 0) {
              console.log('📋 Approved documents (should show QR codes):');
              approvedDocs.forEach(doc => {
                console.log(`  - ${doc.document_name} (ID: ${doc.id})`);
              });
            }
            
            console.log('\n📊 Checking existing archives...');
            
            // Check existing archives and their status
            db.all(`SELECT id, document_name, status, approved_at, approved_by FROM archives ORDER BY id`, (err, archives) => {
              if (err) {
                console.error('❌ Error fetching archives:', err.message);
                reject(err);
                return;
              }
              
              console.log(`📦 Found ${archives.length} archives:`);
              archives.forEach(archive => {
                console.log(`  - ID: ${archive.id}, Name: ${archive.document_name}, Status: ${archive.status || 'N/A'}, Approved: ${archive.approved_at ? 'Yes' : 'No'}, Approved By: ${archive.approved_by || 'N/A'}`);
              });
              
              console.log('\n🎉 Database structure and data check completed!');
              
              // Summary
              console.log('\n📋 Summary:');
              console.log(`✅ Documents table has approved_by column: ${hasApprovedBy}`);
              console.log(`✅ Archives table has status column: ${hasStatus}`);
              console.log(`📄 Total documents: ${docs.length}`);
              console.log(`✅ Approved documents (should show QR): ${approvedDocs.length}`);
              console.log(`📦 Total archives: ${archives.length}`);
              
              if (hasApprovedBy && hasStatus) {
                console.log('\n🎯 Database structure is correct for QR code fix!');
                console.log('💡 QR codes should now display correctly for approved documents.');
                console.log('💡 The status should persist after page refresh since it comes from the database.');
              } else {
                console.log('\n⚠️  Database structure needs fixing!');
                if (!hasApprovedBy) console.log('❌ Missing approved_by column in documents table');
                if (!hasStatus) console.log('❌ Missing status column in archives table');
              }
              
              resolve();
            });
          });
        });
      });
    });
  });
}

// Execute tests
runTests()
  .then(() => {
    console.log('\n✅ Tests completed successfully');
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('🔒 Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Tests failed:', error.message);
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
