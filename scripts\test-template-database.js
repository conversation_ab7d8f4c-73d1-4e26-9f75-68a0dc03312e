const {
  initializeDatabase,
  createTemplate,
  getAllTemplates,
  getTemplatesByUserId,
  getAllUsers,
  closeDatabase
} = require('../src/lib/database.ts');

async function testTemplateDatabaseOperations() {
  console.log('🧪 Testing Template Database Operations\n');

  try {
    // Initialize database
    console.log('📦 Initializing database...');
    await initializeDatabase();
    console.log('✅ Database initialized\n');

    // Check if users exist
    console.log('👥 Checking users...');
    const users = await getAllUsers();
    console.log(`Found ${users.length} users`);
    
    if (users.length === 0) {
      console.log('⚠️ No users found. You need to create a user first.');
      console.log('Run: pnpm run auth:test or create a user through the settings page\n');
      return;
    }

    const testUserId = users[0].id;
    console.log(`Using user ID: ${testUserId} (${users[0].username})\n`);

    // Test creating a template
    console.log('📄 Creating test template...');
    const templateId = await createTemplate(
      'Test Template',
      'A test template for database verification',
      'test_template.html',
      JSON.stringify(['NAME', 'DATE', 'SIGNATURE']),
      'A4',
      testUserId
    );
    console.log(`✅ Template created with ID: ${templateId}\n`);

    // Get all templates
    console.log('📋 Retrieving all templates...');
    const allTemplates = await getAllTemplates();
    console.log(`Found ${allTemplates.length} templates:`);
    allTemplates.forEach(template => {
      console.log(`  - ID: ${template.id}, Name: ${template.template_name}, File: ${template.filename}`);
    });
    console.log('');

    // Get templates by user ID
    console.log(`📋 Retrieving templates for user ${testUserId}...`);
    const userTemplates = await getTemplatesByUserId(testUserId);
    console.log(`Found ${userTemplates.length} templates for this user:`);
    userTemplates.forEach(template => {
      console.log(`  - ID: ${template.id}, Name: ${template.template_name}`);
      console.log(`    Description: ${template.description || 'No description'}`);
      console.log(`    Placeholders: ${template.placeholders || 'None'}`);
      console.log(`    Layout: ${template.layout_size || 'Not specified'}`);
      console.log(`    Uploaded: ${template.uploaded_at}`);
      console.log('');
    });

    console.log('🎉 All database operations completed successfully!');

  } catch (error) {
    console.error('❌ Error during database operations:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    // Close database connection
    await closeDatabase();
    console.log('🔒 Database connection closed');
  }
}

// Run the test
testTemplateDatabaseOperations();
