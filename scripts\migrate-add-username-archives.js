const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'ldis.db');

// Connect to database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error connecting to database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Enable foreign keys
db.run('PRAGMA foreign_keys = ON');

console.log('🚀 Starting migration to add username field to archives table...');

// Run the migration
async function runMigration() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Step 1: Create new archives table with username column
      console.log('📝 Creating new archives table structure...');
      db.run(`
        CREATE TABLE IF NOT EXISTS archives_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          document_name TEXT NOT NULL,
          applicant_name TEXT NOT NULL,
          uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          document_data BLOB,
          status TEXT DEFAULT 'approved',
          user_id INTEGER NOT NULL,
          document_id INTEGER NOT NULL,
          approved_at DATETIME,
          approved_by INTEGER,
          code TEXT,
          username TEXT,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `, (err) => {
        if (err) {
          console.error('❌ Error creating new archives table:', err.message);
          reject(err);
          return;
        }
        console.log('✅ New archives table structure created');

        // Step 2: Get all existing archives with user information
        console.log('📋 Fetching existing archives with user information...');
        db.all(`
          SELECT a.*, u.username 
          FROM archives a 
          LEFT JOIN users u ON a.user_id = u.id
        `, (err, archives) => {
          if (err) {
            console.error('❌ Error fetching archives:', err.message);
            reject(err);
            return;
          }

          console.log(`📊 Found ${archives.length} archives to migrate`);

          if (archives.length === 0) {
            // No archives to migrate, just rename tables
            renameArchivesTable(resolve, reject);
            return;
          }

          // Step 3: Copy archives with username populated
          let completed = 0;
          const total = archives.length;

          archives.forEach((archive) => {
            db.run(`
              INSERT INTO archives_new (
                id, document_name, applicant_name, uploaded_at, document_data, 
                status, user_id, document_id, approved_at, approved_by, code, username
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              archive.id,
              archive.document_name,
              archive.applicant_name,
              archive.uploaded_at,
              archive.document_data,
              archive.status,
              archive.user_id,
              archive.document_id,
              archive.approved_at,
              archive.approved_by,
              archive.code,
              archive.username || null // Use username from join, or null if not found
            ], (err) => {
              if (err) {
                console.error(`❌ Error copying archive ${archive.id}:`, err.message);
                reject(err);
                return;
              }

              completed++;
              console.log(`✅ Copied archive ${archive.id} with username: ${archive.username || 'null'}`);

              if (completed === total) {
                console.log('✅ All archives copied with usernames');
                renameArchivesTable(resolve, reject);
              }
            });
          });
        });
      });
    });
  });
}

// Function to rename tables
function renameArchivesTable(resolve, reject) {
  console.log('🔄 Replacing old archives table...');

  // Drop old table and rename new one
  db.run('DROP TABLE archives', (err) => {
    if (err) {
      console.error('❌ Error dropping old archives table:', err.message);
      reject(err);
      return;
    }

    db.run('ALTER TABLE archives_new RENAME TO archives', (err) => {
      if (err) {
        console.error('❌ Error renaming new archives table:', err.message);
        reject(err);
        return;
      }

      console.log('✅ Archives table updated successfully');
      resolve();
    });
  });
}

// Execute migration
runMigration()
  .then(() => {
    console.log('🎉 Migration completed successfully!');
    console.log('📊 Summary of changes:');
    console.log('   - Added username TEXT column to archives table');
    console.log('   - Populated username field for existing archives based on user_id');
    console.log('   - Preserved all existing archive data and relationships');
    
    // Close database connection
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('✅ Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Migration failed:', error);
    
    // Close database connection
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
