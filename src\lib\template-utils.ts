/**
 * Utility functions for template processing
 */

/**
 * Extract placeholders from HTML content
 * Looks for patterns like [PLACEHOLDER_NAME]
 */
export function extractPlaceholders(htmlContent: string): string[] {
  const placeholderRegex = /\[([A-Z_\s]+)\]/g;
  const placeholders: string[] = [];
  let match;
  
  while ((match = placeholderRegex.exec(htmlContent)) !== null) {
    const placeholder = match[1].trim();
    if (!placeholders.includes(placeholder)) {
      placeholders.push(placeholder);
    }
  }
  
  return placeholders;
}

/**
 * Check if HTML content contains image references
 */
export function hasImageReferences(htmlContent: string): boolean {
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
  return imgRegex.test(htmlContent);
}

/**
 * Extract image source paths from HTML content
 */
export function extractImageSources(htmlContent: string): string[] {
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
  const imageSources: string[] = [];
  let match;
  
  while ((match = imgRegex.exec(htmlContent)) !== null) {
    imageSources.push(match[1]);
  }
  
  return imageSources;
}

/**
 * Extract template name from filename (remove extension)
 */
export function extractTemplateName(filename: string): string {
  return filename.replace(/\.(htm|html)$/i, '');
}

/**
 * Validate HTML file type
 */
export function isValidHtmlFile(filename: string): boolean {
  return /\.(htm|html)$/i.test(filename);
}

/**
 * Validate image file type
 */
export function isValidImageFile(filename: string): boolean {
  return /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(filename);
}

/**
 * Basic HTML content validation
 */
export function isValidHtmlContent(content: string): boolean {
  const lowerContent = content.toLowerCase();
  return lowerContent.includes('<html') || lowerContent.includes('<!doctype');
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Generate unique filename to avoid conflicts
 */
export function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop();
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
  
  return `${nameWithoutExt}_${timestamp}_${random}.${extension}`;
}

/**
 * Sanitize template name for use as directory name
 */
export function sanitizeTemplateName(name: string): string {
  return name
    .replace(/[^a-zA-Z0-9_-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '')
    .toLowerCase();
}
