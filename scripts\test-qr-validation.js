const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🔍 Testing QR code validation system...');
console.log('📍 Database path:', dbPath);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Test the QR code validation system
async function testQRValidation() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      console.log('\n🔍 Checking documents and archives with codes...');
      
      // Check documents table
      db.all(`SELECT id, document_name, applicant_name, status, code, approved_at FROM documents WHERE code IS NOT NULL ORDER BY id`, (err, docs) => {
        if (err) {
          console.error('❌ Error fetching documents:', err.message);
          reject(err);
          return;
        }
        
        console.log(`📄 Found ${docs.length} documents with codes:`);
        
        if (docs.length > 0) {
          console.log('\n📋 Documents with QR codes:');
          docs.forEach((doc, index) => {
            console.log(`${index + 1}. ${doc.document_name} (${doc.applicant_name})`);
            console.log(`   Code: ${doc.code}`);
            console.log(`   Status: ${doc.status}`);
            console.log(`   Approved: ${doc.approved_at ? new Date(doc.approved_at).toLocaleString() : 'N/A'}`);
            console.log(`   QR URL: /validate/${doc.code}`);
            console.log('');
          });
        }
        
        // Check archives table
        db.all(`SELECT id, document_name, applicant_name, status, code, approved_at FROM archives WHERE code IS NOT NULL ORDER BY id`, (err, archives) => {
          if (err) {
            console.error('❌ Error fetching archives:', err.message);
            reject(err);
            return;
          }
          
          console.log(`🗄️  Found ${archives.length} archives with codes:`);
          
          if (archives.length > 0) {
            console.log('\n📋 Archives with QR codes:');
            archives.forEach((archive, index) => {
              console.log(`${index + 1}. ${archive.document_name} (${archive.applicant_name})`);
              console.log(`   Code: ${archive.code}`);
              console.log(`   Status: ${archive.status}`);
              console.log(`   Approved: ${archive.approved_at ? new Date(archive.approved_at).toLocaleString() : 'N/A'}`);
              console.log(`   QR URL: /validate/${archive.code}`);
              console.log('');
            });
          }
          
          const totalWithCodes = docs.length + archives.length;
          const approvedWithCodes = docs.filter(d => d.status === 'approved').length + 
                                   archives.filter(a => a.status === 'approved').length;
          
          console.log('📊 Summary:');
          console.log(`• Total documents/archives with codes: ${totalWithCodes}`);
          console.log(`• Approved documents/archives with codes: ${approvedWithCodes}`);
          console.log(`• Documents that will show QR codes: ${approvedWithCodes}`);
          
          if (approvedWithCodes > 0) {
            console.log('\n🎯 QR Code & Validation Test Instructions:');
            console.log('1. Open the LDIS application in your browser');
            console.log('2. Navigate to Administration > Documents');
            console.log('3. Click on any approved document from the list above');
            console.log('4. You should see a QR code in the bottom-left of the preview');
            console.log('5. The QR code now contains a validation URL (not just "test")');
            console.log('6. Scan the QR code with your phone or copy the validation URL');
            console.log('7. The QR code should redirect to: /validate/[CODE]');
            console.log('8. The validation page should show document details and "Valid Document" ✅');
            
            console.log('\n📱 Manual Testing URLs:');
            [...docs, ...archives]
              .filter(item => item.status === 'approved' && item.code)
              .forEach((item, index) => {
                console.log(`${index + 1}. http://localhost:3000/validate/${item.code}`);
                console.log(`   → Should show: ${item.document_name} (${item.applicant_name})`);
              });
          } else {
            console.log('\n⚠️  No approved documents with codes found.');
            console.log('💡 To test QR validation:');
            console.log('   1. Create a new document by going to Apply > [Template]');
            console.log('   2. Fill out the form and submit');
            console.log('   3. Go to Administration > Documents');
            console.log('   4. Click on the new document and approve it');
            console.log('   5. The QR code should appear with a validation URL');
            console.log('   6. Scan or visit the URL to test validation');
          }
          
          console.log('\n🔧 Technical Details:');
          console.log('• QR codes now contain validation URLs: /validate/[CODE]');
          console.log('• Validation API endpoint: /api/validate/[code]');
          console.log('• Checks both documents and archives tables');
          console.log('• Only approved documents show as valid');
          console.log('• QR codes are 80x80px in bottom-left corner');
          
          resolve();
        });
      });
    });
  });
}

// Execute test
testQRValidation()
  .then(() => {
    console.log('\n✅ QR code validation test completed');
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('🔒 Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Test failed:', error.message);
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
