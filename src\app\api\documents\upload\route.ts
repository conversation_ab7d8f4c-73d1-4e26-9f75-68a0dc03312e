import { NextRequest, NextResponse } from 'next/server';
import { createDocument, createNotification } from '@/lib/database';
import { extractMetadataFromPdfText } from '@/lib/pdf-metadata-utils';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);

// PDF parsing function using pdf-parse with error handling
async function parsePdf(buffer: Buffer) {
  try {
    // Create a temporary directory to avoid test file conflicts
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Write buffer to temporary file
    const tempFilePath = path.join(tempDir, `parse_${Date.now()}.pdf`);
    await writeFile(tempFilePath, buffer);

    // Use pdf-parse with the file path to avoid buffer issues
    const pdfParse = await import('pdf-parse');
    const pdfData = await pdfParse.default(buffer);

    // Clean up temporary file
    if (fs.existsSync(tempFilePath)) {
      await unlink(tempFilePath);
    }

    return pdfData;
  } catch (error) {
    // If pdf-parse fails, try to extract text directly from buffer
    console.warn('pdf-parse failed, attempting direct text extraction:', error instanceof Error ? error.message : String(error));

    // Convert buffer to string and look for text content
    const bufferString = buffer.toString('utf8');

    // Look for LDIS metadata specifically
    if (bufferString.includes('LDIS_METADATA:')) {
      return {
        text: bufferString,
        numpages: 1
      };
    }

    throw error;
  }
}

/**
 * POST /api/documents/upload - Upload PDF document and extract metadata
 */
export async function POST(request: NextRequest) {
  let tempFilePath: string | null = null;
  
  try {
    const formData = await request.formData();
    const pdfFile = formData.get('pdfFile') as File;

    // Validate required fields
    if (!pdfFile) {
      return NextResponse.json(
        { error: 'PDF file is required' },
        { status: 400 }
      );
    }

    // Validate file type
    if (pdfFile.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'Only PDF files are allowed' },
        { status: 400 }
      );
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (pdfFile.size > maxSize) {
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    console.log('Processing PDF upload:', {
      fileName: pdfFile.name,
      fileSize: pdfFile.size,
      fileType: pdfFile.type
    });

    // Convert file to buffer
    const arrayBuffer = await pdfFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Create temporary file for pdf-parse
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    tempFilePath = path.join(tempDir, `upload_${Date.now()}_${pdfFile.name}`);
    await writeFile(tempFilePath, buffer);

    // Extract text from PDF
    let pdfData;
    try {
      pdfData = await parsePdf(buffer);
    } catch (pdfError) {
      console.error('Error parsing PDF:', pdfError);
      return NextResponse.json(
        { error: 'Failed to parse PDF file. Please ensure it is a valid PDF document.' },
        { status: 400 }
      );
    }

    console.log('PDF parsed successfully:', {
      pages: pdfData.numpages,
      textLength: pdfData.text.length
    });

    // Extract metadata from PDF text
    const extractedMetadata = extractMetadataFromPdfText(pdfData.text);
    
    if (!extractedMetadata) {
      return NextResponse.json(
        { error: 'No LDIS metadata found in the PDF. Please ensure this is a document generated by the LDIS system.' },
        { status: 400 }
      );
    }

    console.log('Metadata extracted successfully:', {
      documentName: extractedMetadata.document_name,
      applicantName: extractedMetadata.applicant_name,
      system: extractedMetadata.generation_info.system,
      fieldCount: Object.keys(extractedMetadata.document_data).length
    });

    // Get current user (since this is a single-user system, get the first user)
    const userResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/auth/me`);
    if (!userResponse.ok) {
      return NextResponse.json(
        { error: 'Failed to get user information. Please ensure you are authenticated.' },
        { status: 401 }
      );
    }
    
    const userData = await userResponse.json();

    // Store document data as JSON string in the database
    const documentDataJson = JSON.stringify(extractedMetadata.document_data);

    // Insert document into database
    const documentId = await createDocument(
      extractedMetadata.document_name,
      extractedMetadata.applicant_name,
      Buffer.from(documentDataJson), // Store as BLOB
      'to review', // Status
      userData.id
    );

    console.log('Document saved to database with ID:', documentId);

    // Create notification for the document upload
    try {
      console.log('Creating notification with params:', {
        documentId,
        documentName: extractedMetadata.document_name,
        applicantName: extractedMetadata.applicant_name,
        userId: userData.id
      });

      const notificationId = await createNotification(
        documentId,
        extractedMetadata.document_name,
        extractedMetadata.applicant_name,
        userData.id
      );
      console.log('Notification created for document upload with ID:', notificationId);
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
      if (notificationError instanceof Error) {
        console.error('Notification error details:', {
          message: notificationError.message,
          code: (notificationError as any).code,
          errno: (notificationError as any).errno
        });
      }
      // Don't fail the entire upload if notification creation fails
    }

    // Clean up temporary file
    if (tempFilePath && fs.existsSync(tempFilePath)) {
      await unlink(tempFilePath);
      console.log('Temporary file deleted:', tempFilePath);
    }

    return NextResponse.json(
      {
        message: 'Document uploaded and processed successfully',
        documentId,
        extractedData: extractedMetadata
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Error processing PDF upload:', error);
    
    // Clean up temporary file in case of error
    if (tempFilePath && fs.existsSync(tempFilePath)) {
      try {
        await unlink(tempFilePath);
        console.log('Temporary file cleaned up after error:', tempFilePath);
      } catch (cleanupError) {
        console.error('Error cleaning up temporary file:', cleanupError);
      }
    }
    
    return NextResponse.json(
      { error: 'Failed to process PDF upload' },
      { status: 500 }
    );
  }
}
