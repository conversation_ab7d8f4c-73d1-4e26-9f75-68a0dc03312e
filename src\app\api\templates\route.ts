import { NextResponse } from 'next/server';
import { getAllTemplates, getTemplatesWithUserInfo } from '@/lib/database';

/**
 * GET /api/templates - Get all templates with user information
 */
export async function GET() {
  try {
    const templates = await getTemplatesWithUserInfo();
    
    // Parse placeholders from JSON string to array for easier frontend handling
    const templatesWithParsedPlaceholders = templates.map(template => ({
      ...template,
      placeholders: template.placeholders ? JSON.parse(template.placeholders) : []
    }));
    
    return NextResponse.json({ 
      templates: templatesWithParsedPlaceholders,
      count: templatesWithParsedPlaceholders.length 
    });
  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    );
  }
}
