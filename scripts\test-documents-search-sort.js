#!/usr/bin/env node

/**
 * Test script for documents page search and sort functionality
 * This script tests the search and sort utilities with sample document data
 */

const { searchFilterSort } = require("../src/lib/search-sort-utils.ts");

console.log("📄 Testing Documents Page Search and Sort Functionality\n");

// Sample document data matching the Document interface
const sampleDocuments = [
  {
    id: 1,
    document_name: "Medical Certificate",
    applicant_name: "<PERSON>",
    uploaded_at: "2025-01-01T10:00:00Z",
    document_data: null,
    status: "pending",
    approved_at: null,
    user_id: 1,
  },
  {
    id: 2,
    document_name: "Birth Certificate",
    applicant_name: "<PERSON>",
    uploaded_at: "2025-01-02T11:00:00Z",
    document_data: null,
    status: "approved",
    approved_at: "2025-01-02T15:00:00Z",
    user_id: 2,
  },
  {
    id: 3,
    document_name: "Marriage Certificate",
    applicant_name: "<PERSON>",
    uploaded_at: "2025-01-03T12:00:00Z",
    document_data: null,
    status: "to review",
    approved_at: null,
    user_id: 3,
  },
  {
    id: 4,
    document_name: "Death Certificate",
    applicant_name: "<PERSON>",
    uploaded_at: "2025-01-04T13:00:00Z",
    document_data: null,
    status: "rejected",
    approved_at: null,
    user_id: 4,
  },
  {
    id: 5,
    document_name: "Business License",
    applicant_name: "Charlie Wilson",
    uploaded_at: "2025-01-05T14:00:00Z",
    document_data: null,
    status: "uploaded",
    approved_at: null,
    user_id: 5,
  },
];

function testDocumentSearch() {
  console.log("🔍 Testing Document Search:");
  
  // Test search by document name
  const searchResults1 = searchFilterSort(sampleDocuments, {
    searchTerm: "certificate",
    searchFields: ["document_name", "applicant_name"],
  });
  console.log(`✅ Search for "certificate": Found ${searchResults1.length} results`);
  console.log(`   - ${searchResults1.map(d => d.document_name).join(", ")}`);
  
  // Test search by applicant name
  const searchResults2 = searchFilterSort(sampleDocuments, {
    searchTerm: "john",
    searchFields: ["document_name", "applicant_name"],
  });
  console.log(`✅ Search for "john": Found ${searchResults2.length} results`);
  console.log(`   - ${searchResults2.map(d => `${d.document_name} (${d.applicant_name})`).join(", ")}`);
  
  // Test case-insensitive search
  const searchResults3 = searchFilterSort(sampleDocuments, {
    searchTerm: "MEDICAL",
    searchFields: ["document_name", "applicant_name"],
  });
  console.log(`✅ Case-insensitive search for "MEDICAL": Found ${searchResults3.length} results`);
  console.log(`   - ${searchResults3.map(d => d.document_name).join(", ")}`);
}

function testDocumentFilters() {
  console.log("\n🔽 Testing Document Filters:");
  
  // Test status filter - approved
  const filterResults1 = searchFilterSort(sampleDocuments, {
    filters: [{ field: "status", values: ["approved"] }],
  });
  console.log(`✅ Filter by "approved": Found ${filterResults1.length} results`);
  console.log(`   - ${filterResults1.map(d => `${d.document_name} (${d.status})`).join(", ")}`);
  
  // Test status filter - pending
  const filterResults2 = searchFilterSort(sampleDocuments, {
    filters: [{ field: "status", values: ["pending"] }],
  });
  console.log(`✅ Filter by "pending": Found ${filterResults2.length} results`);
  console.log(`   - ${filterResults2.map(d => `${d.document_name} (${d.status})`).join(", ")}`);
  
  // Test multiple status filter (to review workflow)
  const filterResults3 = searchFilterSort(sampleDocuments, {
    filters: [{ field: "status", values: ["pending", "uploaded", "to review"] }],
  });
  console.log(`✅ Filter by "to review" statuses: Found ${filterResults3.length} results`);
  console.log(`   - ${filterResults3.map(d => `${d.document_name} (${d.status})`).join(", ")}`);
  
  // Test rejected filter
  const filterResults4 = searchFilterSort(sampleDocuments, {
    filters: [{ field: "status", values: ["rejected"] }],
  });
  console.log(`✅ Filter by "rejected": Found ${filterResults4.length} results`);
  console.log(`   - ${filterResults4.map(d => `${d.document_name} (${d.status})`).join(", ")}`);
}

function testDocumentSort() {
  console.log("\n📊 Testing Document Sort:");
  
  // Test sort by document name (ascending)
  const sortResults1 = searchFilterSort(sampleDocuments, {
    sortField: "document_name",
    sortOrder: "asc",
  });
  console.log(`✅ Sort by document name (asc):`);
  console.log(`   - ${sortResults1.map(d => d.document_name).join(", ")}`);
  
  // Test sort by applicant name (descending)
  const sortResults2 = searchFilterSort(sampleDocuments, {
    sortField: "applicant_name",
    sortOrder: "desc",
  });
  console.log(`✅ Sort by applicant name (desc):`);
  console.log(`   - ${sortResults2.map(d => d.applicant_name).join(", ")}`);
  
  // Test sort by upload date (newest first - default)
  const sortResults3 = searchFilterSort(sampleDocuments, {
    sortField: "uploaded_at",
    sortOrder: "desc",
  });
  console.log(`✅ Sort by upload date (desc - newest first):`);
  console.log(`   - ${sortResults3.map(d => `${d.document_name} (${d.uploaded_at.split('T')[0]})`).join(", ")}`);
  
  // Test sort by status
  const sortResults4 = searchFilterSort(sampleDocuments, {
    sortField: "status",
    sortOrder: "asc",
  });
  console.log(`✅ Sort by status (asc):`);
  console.log(`   - ${sortResults4.map(d => `${d.document_name} (${d.status})`).join(", ")}`);
}

function testCombinedOperations() {
  console.log("\n🔄 Testing Combined Operations:");
  
  // Test search + filter + sort
  const combinedResults1 = searchFilterSort(sampleDocuments, {
    searchTerm: "certificate",
    searchFields: ["document_name", "applicant_name"],
    filters: [{ field: "status", values: ["approved", "pending"] }],
    sortField: "applicant_name",
    sortOrder: "asc",
  });
  console.log(`✅ Search "certificate" + filter approved/pending + sort by applicant:`);
  console.log(`   - Found ${combinedResults1.length} results`);
  combinedResults1.forEach(d => {
    console.log(`     * ${d.document_name} - ${d.applicant_name} (${d.status})`);
  });
  
  // Test complex filter scenario (to review workflow)
  const combinedResults2 = searchFilterSort(sampleDocuments, {
    searchTerm: "",
    searchFields: ["document_name", "applicant_name"],
    filters: [{ field: "status", values: ["pending", "uploaded", "to review"] }],
    sortField: "uploaded_at",
    sortOrder: "desc",
  });
  console.log(`✅ Filter "to review" workflow + sort by upload date (newest first):`);
  console.log(`   - Found ${combinedResults2.length} results`);
  combinedResults2.forEach(d => {
    console.log(`     * ${d.document_name} - ${d.status} (${d.uploaded_at.split('T')[0]})`);
  });
  
  // Test search with no results
  const combinedResults3 = searchFilterSort(sampleDocuments, {
    searchTerm: "nonexistent",
    searchFields: ["document_name", "applicant_name"],
    filters: [],
    sortField: "document_name",
    sortOrder: "asc",
  });
  console.log(`✅ Search for "nonexistent": Found ${combinedResults3.length} results (should be 0)`);
}

function testDocumentWorkflow() {
  console.log("\n⚙️  Testing Document Workflow Scenarios:");
  
  // Scenario 1: Admin reviewing pending documents
  const pendingReview = searchFilterSort(sampleDocuments, {
    filters: [{ field: "status", values: ["pending", "uploaded", "to review"] }],
    sortField: "uploaded_at",
    sortOrder: "asc", // oldest first for review queue
  });
  console.log(`✅ Documents needing review (oldest first): ${pendingReview.length} documents`);
  pendingReview.forEach(d => {
    console.log(`   - ${d.document_name} by ${d.applicant_name} (${d.status}) - ${d.uploaded_at.split('T')[0]}`);
  });
  
  // Scenario 2: Finding approved documents for archiving
  const approvedDocs = searchFilterSort(sampleDocuments, {
    filters: [{ field: "status", values: ["approved"] }],
    sortField: "approved_at",
    sortOrder: "desc",
  });
  console.log(`✅ Approved documents for archiving: ${approvedDocs.length} documents`);
  approvedDocs.forEach(d => {
    console.log(`   - ${d.document_name} by ${d.applicant_name} (approved: ${d.approved_at?.split('T')[0] || 'N/A'})`);
  });
  
  // Scenario 3: Searching for specific applicant's documents
  const applicantDocs = searchFilterSort(sampleDocuments, {
    searchTerm: "smith",
    searchFields: ["applicant_name"],
    sortField: "uploaded_at",
    sortOrder: "desc",
  });
  console.log(`✅ Documents for applicant "smith": ${applicantDocs.length} documents`);
  applicantDocs.forEach(d => {
    console.log(`   - ${d.document_name} (${d.status}) - ${d.uploaded_at.split('T')[0]}`);
  });
}

// Run all tests
try {
  testDocumentSearch();
  testDocumentFilters();
  testDocumentSort();
  testCombinedOperations();
  testDocumentWorkflow();
  
  console.log("\n\n🎉 All documents search and sort tests completed successfully!");
  console.log("\n📱 Features Tested:");
  console.log("✅ Text search across document name and applicant name");
  console.log("✅ Status filtering (pending, approved, rejected, to review workflow)");
  console.log("✅ Sort by document name, applicant name, upload date, status");
  console.log("✅ Combined search + filter + sort operations");
  console.log("✅ Document workflow scenarios (review queue, archiving, applicant lookup)");
  console.log("✅ Case-insensitive search and edge cases");
  
  console.log("\n🎯 Documents Page Ready:");
  console.log("• Search by document name or applicant name");
  console.log("• Filter by status (pending, uploaded, to review, approved, rejected)");
  console.log("• Sort by any column with ascending/descending order");
  console.log("• Combined operations for complex document management workflows");
  console.log("• Consistent UI with notifications and archives pages");
  
} catch (error) {
  console.error("❌ Test failed:", error.message);
  process.exit(1);
}
