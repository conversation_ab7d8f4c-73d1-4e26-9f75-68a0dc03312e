const {
  initializeDatabase,
  getAllTemplates,
  getTemplatesWithUserInfo,
  closeDatabase
} = require('../src/lib/database.ts');

async function checkTemplates() {
  console.log('📋 Checking Templates in Database\n');

  try {
    // Initialize database
    await initializeDatabase();

    // Get all templates
    console.log('📄 All Templates:');
    const allTemplates = await getAllTemplates();
    
    if (allTemplates.length === 0) {
      console.log('❌ No templates found in database');
    } else {
      console.log(`✅ Found ${allTemplates.length} templates:\n`);
      
      allTemplates.forEach((template, index) => {
        console.log(`${index + 1}. Template ID: ${template.id}`);
        console.log(`   Name: ${template.template_name}`);
        console.log(`   Description: ${template.description || 'No description'}`);
        console.log(`   Filename: ${template.filename}`);
        console.log(`   Layout Size: ${template.layout_size || 'Not specified'}`);
        console.log(`   User ID: ${template.user_id}`);
        console.log(`   Uploaded: ${template.uploaded_at}`);
        
        // Parse and display placeholders
        if (template.placeholders) {
          try {
            const placeholders = JSON.parse(template.placeholders);
            console.log(`   Placeholders: [${placeholders.join(', ')}]`);
          } catch (e) {
            console.log(`   Placeholders: ${template.placeholders}`);
          }
        } else {
          console.log(`   Placeholders: None`);
        }
        console.log('');
      });
    }

    // Get templates with user info
    console.log('👥 Templates with User Information:');
    const templatesWithUsers = await getTemplatesWithUserInfo();
    
    if (templatesWithUsers.length > 0) {
      templatesWithUsers.forEach((template, index) => {
        console.log(`${index + 1}. ${template.template_name} (by ${template.username})`);
        console.log(`   Uploaded: ${template.uploaded_at}`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking templates:', error);
  } finally {
    await closeDatabase();
    console.log('\n🔒 Database connection closed');
  }
}

checkTemplates();
