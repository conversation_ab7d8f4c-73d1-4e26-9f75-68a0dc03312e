const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'ldis.db');

// Connect to database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error connecting to database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Enable foreign keys
db.run('PRAGMA foreign_keys = ON');

console.log('🚀 Starting migration to add username field to notifications table...');

// Run the migration
async function runMigration() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Step 1: Create new notifications table with username column
      console.log('📝 Creating new notifications table structure...');
      db.run(`
        CREATE TABLE IF NOT EXISTS notifications_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          document_id INTEGER NOT NULL,
          document_name TEXT NOT NULL,
          applicant_name TEXT NOT NULL,
          is_read BOOLEAN DEFAULT FALSE,
          uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          user_id INTEGER NOT NULL,
          username TEXT,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `, (err) => {
        if (err) {
          console.error('❌ Error creating new notifications table:', err.message);
          reject(err);
          return;
        }
        console.log('✅ New notifications table structure created');

        // Step 2: Get all existing notifications with user information
        console.log('📋 Fetching existing notifications with user information...');
        db.all(`
          SELECT n.*, u.username 
          FROM notifications n 
          LEFT JOIN users u ON n.user_id = u.id
        `, (err, notifications) => {
          if (err) {
            console.error('❌ Error fetching notifications:', err.message);
            reject(err);
            return;
          }

          console.log(`📊 Found ${notifications.length} notifications to migrate`);

          if (notifications.length === 0) {
            // No notifications to migrate, just rename tables
            renameNotificationsTable(resolve, reject);
            return;
          }

          // Step 3: Copy notifications with username populated
          let completed = 0;
          const total = notifications.length;

          notifications.forEach((notification) => {
            db.run(`
              INSERT INTO notifications_new (
                id, document_id, document_name, applicant_name, is_read, 
                uploaded_at, user_id, username
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              notification.id,
              notification.document_id,
              notification.document_name,
              notification.applicant_name,
              notification.is_read,
              notification.uploaded_at,
              notification.user_id,
              notification.username || null // Use username from join, or null if not found
            ], (err) => {
              if (err) {
                console.error(`❌ Error copying notification ${notification.id}:`, err.message);
                reject(err);
                return;
              }

              completed++;
              console.log(`✅ Copied notification ${notification.id} with username: ${notification.username || 'null'}`);

              if (completed === total) {
                console.log('✅ All notifications copied with usernames');
                renameNotificationsTable(resolve, reject);
              }
            });
          });
        });
      });
    });
  });
}

// Function to rename tables
function renameNotificationsTable(resolve, reject) {
  console.log('🔄 Replacing old notifications table...');

  // Drop old table and rename new one
  db.run('DROP TABLE notifications', (err) => {
    if (err) {
      console.error('❌ Error dropping old notifications table:', err.message);
      reject(err);
      return;
    }

    db.run('ALTER TABLE notifications_new RENAME TO notifications', (err) => {
      if (err) {
        console.error('❌ Error renaming new notifications table:', err.message);
        reject(err);
        return;
      }

      console.log('✅ Notifications table updated successfully');
      resolve();
    });
  });
}

// Execute migration
runMigration()
  .then(() => {
    console.log('🎉 Migration completed successfully!');
    console.log('📊 Summary of changes:');
    console.log('   - Added username TEXT column to notifications table');
    console.log('   - Populated username field for existing notifications based on user_id');
    console.log('   - Preserved all existing notification data and relationships');
    
    // Close database connection
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
        process.exit(1);
      }
      console.log('✅ Database connection closed');
      process.exit(0);
    });
  })
  .catch((error) => {
    console.error('❌ Migration failed:', error);
    
    // Close database connection
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
